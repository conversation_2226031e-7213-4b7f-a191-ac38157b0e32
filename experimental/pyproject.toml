[build-system]
requires = ["setuptools>=64", "setuptools_scm>=8"]
build-backend = "setuptools.build_meta"

[project]
name = "ragas_experimental"
description = "Experimental extensions for Ragas"
requires-python = ">=3.9"
authors = [
    {name = "jjmachan", email = "<EMAIL>"},
    {name = "ikka", email = "<EMAIL>"}
]
license = {text = "Apache-2.0"}
keywords = ["jupyter", "notebook", "python", "evaluation", "llm", "ragas"]
classifiers = [
    "Development Status :: 3 - Alpha",
    "Intended Audience :: Developers",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
]
dependencies = [
    "tqdm",
    "instructor",
    "pydantic",
    "numpy",
    "gitpython",
    "typer",
    "rich",
]
# Only version remains dynamic (managed by setuptools_scm)
dynamic = ["version"]
readme = "README.md"

[project.optional-dependencies]
all = ["pandas"]
examples = ["openai>=1.0.0"]
tracing = ["langfuse", "mlflow"]

[project.entry-points."ragas.backends"]
"local/csv" = "ragas_experimental.backends.local_csv:LocalCSVBackend"
"local/jsonl" = "ragas_experimental.backends.local_jsonl:LocalJSONLBackend"
"inmemory" = "ragas_experimental.backends.inmemory:InMemoryBackend"

[tool.setuptools.packages.find]
include = ["ragas_experimental*", "ragas_examples*"]
exclude = ["site*", "old_nbs*", "experiments*", "_proc*", "build*", "dist*"]

[project.scripts]
ragas = "ragas_experimental.cli:app"

[tool.setuptools_scm]
root = ".."  # Points to monorepo root, one directory up
version_file = "ragas_experimental/_version.py"  # Creates a version file

[dependency-groups]
dev = [
    "pytest>=7.0.0",
    "pytest-asyncio>=0.21.0", 
    "pytest-mock>=3.10.0",
    "black",
    "ruff",
    "vcrpy",
    "pytest-vcr",
]
box = [
    "boxsdk[jwt]",
]
test = []

[tool.pytest.ini_options]
asyncio_default_fixture_loop_scope = "function"
markers = [
    "experimental_ci: Set of tests that will be run as part of Experimental CI",
    "e2e: End-to-End tests for Experimental",
]

# Ruff configuration is inherited from workspace.toml at the monorepo root
