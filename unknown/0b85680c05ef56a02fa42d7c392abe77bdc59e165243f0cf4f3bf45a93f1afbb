{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# RunConfig\n", "\n", "The `RunConfig` allows you to pass in the run parameters to functions like `evaluate()` and `TestsetGenerator.generate()`. Depending on your LLM providers rate limits, SLAs and traffic, controlling these parameters can improve the speed and reliablility of Ragas runs.\n", "\n", "How to configure the `RunConfig` in\n", "\n", "- [Evaluate](#evaluate)\n", "- [TestsetGenerator]()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Rate Limits\n", "\n", "Ragas leverages parallelism with Async in python but the `RunConfig` has a field called `max_workers` which control the number of concurent requests allowed together. You adjust this to get the maximum concurency your provider allows"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["from ragas.run_config import RunConfig\n", "\n", "# increasing max_workers to 64 and timeout to 60 seconds\n", "\n", "my_run_config = RunConfig(max_workers=64, timeout=60)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Evaluate"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ragas import EvaluationDataset, SingleTurnSample\n", "from ragas.metrics import Faithfulness\n", "from datasets import load_dataset\n", "from ragas import evaluate\n", "\n", "dataset = load_dataset(\"explodinggradients/amnesty_qa\", \"english_v3\")\n", "\n", "samples = []\n", "for row in dataset[\"eval\"]:\n", "    sample = SingleTurnSample(\n", "        user_input=row[\"user_input\"],\n", "        reference=row[\"reference\"],\n", "        response=row[\"response\"],\n", "        retrieved_contexts=row[\"retrieved_contexts\"],\n", "    )\n", "    samples.append(sample)\n", "\n", "eval_dataset = EvaluationDataset(samples=samples)\n", "metric = Faithfulness()\n", "\n", "_ = evaluate(\n", "    dataset=eval_dataset,\n", "    metrics=[metric],\n", "    run_config=my_run_config,\n", ")"]}], "metadata": {"kernelspec": {"display_name": "ragas", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 2}