# Security Policy

## Reporting Security Issues

We take the security of RAGAS seriously. If you discover a security vulnerability in this project, please report it to us privately. **Do not report security vulnerabilities through public GitHub issues, discussions, or pull requests.**

To report a vulnerability, please email <NAME_EMAIL>. While not all details are mandatory, providing as much information as possible will assist us in effectively triaging and addressing the issue. Please include:

- **Type of Issue**: (e.g., buffer overflow, SQL injection, cross-site scripting)
- **Affected Versions**: List the versions of RAGAS impacted by this vulnerability.
- **Affected Files**: Full paths of source files related to the issue.
- **Location in Code**: The location of the affected source code (tag/branch/commit or direct URL).
- **Configuration Details**: Any special configuration required to reproduce the issue.
- **Environment**: (e.g., Linux / Windows / macOS)
- **Reproduction Steps**: Step-by-step instructions to reproduce the issue.
- **Proof-of-Concept or Exploit Code**: (if possible)
- **Impact Assessment**: Description of the issue's impact and how an attacker might exploit it.
- **Mitigation Suggestions**: If possible, offer suggestions or patches to mitigate the issue.

This information will help us triage and address your report more quickly.

## Supported Versions

The following versions of RAGAS are currently being supported with security updates.

| Version | Supported          |
| ------- | ------------------ |
| 0.2.x   | :white_check_mark: |
| 0.1.x   | :x:                |
| < 0.1.x | :x: |


## Security Update Policy

Upon receiving a security report, we will:

1. Acknowledge receipt within 48 hours.
2. Investigate and verify the issue.
3. Develop a fix and prepare a release.
4. Coordinate with the reporter to validate the fix.
5. Release the fix and update all affected parties.

We aim to address critical issues within 7 days of disclosure.

## Preferred Languages

We prefer all communications to be in English.

## Policy

We follow the principle of [Coordinated Vulnerability Disclosure.](https://en.wikipedia.org/wiki/Coordinated_vulnerability_disclosure)

## Acknowledgments

We appreciate the efforts of security researchers and users who report vulnerabilities to us. Your contributions help improve the security of RAGAS.

## References

For more information on security reporting and policies, you may refer to:

- [GitHub's Guide to Reporting Security Vulnerabilities](https://docs.github.com/en/code-security/security-advisories/guidelines-for-reporting-and-writing-about-security-vulnerabilities)
- [Open Source Security Foundation (OpenSSF) Best Practices](https://bestpractices.coreinfrastructure.org/)

---

*This policy is subject to change without notice. Please refer to the latest version in our repository.*
