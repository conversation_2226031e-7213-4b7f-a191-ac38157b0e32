# Testset Generation

Curating a high quality test dataset is crucial for evaluating the performance of your AI application.

## Characteristics of an Ideal Test Dataset

- Contains high quality data samples
- Covers wide variety of scenarios as observed in real world. 
- Contains enough number of samples to be derive statistically significant conclusions. 
- Continually updated to prevent data drift

Curating such a dataset manually can be time consuming and expensive. Ragas provides a set of tools to generate synthetic test datasets for evaluating your AI applications. 

<div class="grid cards" markdown>

- :fontawesome-solid-database:[__RAG__ for evaluating retrieval augmented generation pipelines](rag.md)
- :fontawesome-solid-robot: [__Agents or Tool use__ for evaluating agent workflows](agents.md)
</div>