{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Athina AI\n", "## Ragas Metrics on your Production Logs\n", "\n", "[Athina](https://athina.ai) is a production monitoring and evaluation platform. Try the [sandbox](https://demo.athina.ai/observe?filters=dateSpan%3D30) here.\n", "\n", "You can use [<PERSON><PERSON><PERSON> with Ragas](http://localhost:3001/evals/preset_evals/ragas_evals) metrics to run evals on production logs, and get granular model performance metrics on your production data.\n", "\n", "![Athina Performance Metrics](https://docs.athina.ai/performance-metrics.png)\n", "\n", "For example, you can get insights like this visually:\n", "- What is my `AnswerRelevancy` score for queries related to `refunds` for customer id `nike-usa`\n", "- What is my `Faithfulness` score for `product catalog` queries using prompt `catalog_answerer/v3` with model `gpt-3.5-turbo`"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ▷ Running Athina Programmatically\n", "\n", "When you use <PERSON><PERSON><PERSON> to run Ragas evals programmatically, you will be able to view the results on Athina's UI like this 👇\n", "\n", "![View RAGAS Metrics on Athina](https://docs.athina.ai/ragas-develop-view.png)\n", "\n", "1. Install Athina's Python SDK:\n", "\n", "```\n", "pip install athina\n", "```\n", "\n", "2. Create an account at [app.athina.ai](https://app.athina.ai). After signing up, you will receive an API key.\n", "\n", "Here's a sample notebook you can follow: https://github.com/athina-ai/athina-evals/blob/main/examples/ragas.ipynb\n", "\n", "3. Run the code"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import os\n", "from athina.evals import (\n", "    RagasAnswerCorrectness,\n", "    RagasAnswerRelevancy,\n", "    RagasContextRelevancy,\n", "    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "from athina.loaders import RagasLoader\n", "from athina.keys import AthinaApiKey, OpenAiApiKey\n", "from athina.runner.run import <PERSON><PERSON><PERSON><PERSON><PERSON>\n", "import pandas as pd\n", "\n", "# Set your API keys\n", "OpenAiApiKey.set_key(os.getenv(\"OPENAI_API_KEY\"))\n", "AthinaApiKey.set_key(os.getenv(\"ATHINA_API_KEY\"))\n", "\n", "# Load your dataset from a dictionary, json, or csv: https://docs.athina.ai/evals/loading_data\n", "dataset = RagasLoader().load_json(\"raw_data.json\")\n", "\n", "# Configure the eval suite\n", "eval_model = \"gpt-3.5-turbo\"\n", "eval_suite = [\n", "    RagasAnswerCorrectness(),\n", "    RagasFaith<PERSON>(),\n", "    RagasContextRelevancy(),\n", "    RagasAnswerRelevancy(),\n", "]\n", "\n", "# Run the evaluation suite\n", "batch_eval_result = EvalRunner.run_suite(\n", "    evals=eval_suite,\n", "    data=dataset,\n", "    max_parallel_evals=1,  # If you increase this, you may run into rate limits\n", ")\n", "\n", "pd.DataFrame(batch_eval_result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### ▷ Configure Ragas to run automatically on your production logs\n", "\n", "If you are [logging your production inferences to <PERSON><PERSON><PERSON>](https://docs.athina.ai/logging/log_via_api), you can configure Ragas metrics to run automatically against your production logs.\n", "\n", "1. Navigate to the [Athina Dashboard](https://app.athina.ai/evals/config)\n", "   \n", "2. Open the **Evals** page (lightning icon on the left)\n", "3. Click the \"New Eval\" button on the top right\n", "4. Select the **Ragas** tab\n", "5. Select the eval you want to configure\n", "\n", "![Set up Ragas on Athina UI](https://docs.athina.ai/ragas-modal-bg.png)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Learn more about <PERSON><PERSON><PERSON>\n", "- **Website:** [https://athina.ai](https://athina.ai)\n", "- **Docs:** [https://docs.athina.ai](https://docs.athina.ai)\n", "- **Github Library:** [https://github.com/athina-ai/athina-evals](https://github.com/athina-ai/athina-evals)\n", "- **Sandbox**: [https://demo.athina.ai](https://demo.athina.ai/observe?filters=dateSpan%3D30)"]}], "metadata": {"kernelspec": {"display_name": "zeno-build", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}}, "nbformat": 4, "nbformat_minor": 2}