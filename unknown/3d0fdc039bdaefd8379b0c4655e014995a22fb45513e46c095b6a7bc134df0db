# Applications

Ragas in action. Examples of how to use Ragas in various applications and
usecases to solve problems you might encounter when you're building.


## Metrics

- [Debug LLM based metrics using tracing](_metrics_llm_calls.md)
- [Evaluating Multi-turn Conversations](evaluating_multi_turn_conversations.md)
- [Estimate cost of evaluation](_cost.md)
- [Evaluations with Vertex AI models](vertexai_x_ragas.md)

## Testset Generation:

- [Single-hop Query Testset](singlehop_testset_gen.md)

## Benchmarking
- [Benchmarking Gemini models](gemini_benchmarking.md)