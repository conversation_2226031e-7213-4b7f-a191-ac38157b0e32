{"cells": [{"cell_type": "markdown", "id": "586226e7", "metadata": {}, "source": ["# Langchain\n", "## Evaluating Langchain QA Chains\n", "\n", "LangChain is a framework for developing applications powered by language models. It can also be used to create RAG systems (or QA systems as they are reffered to in langchain). If you want to know more about creating RAG systems with langchain you can check the [docs](https://python.langchain.com/docs/use_cases/question_answering/).\n", "\n", "With this integration you can easily evaluate your QA chains with the metrics offered in ragas"]}, {"cell_type": "code", "execution_count": null, "id": "cc3fe0c6", "metadata": {}, "outputs": [], "source": ["#!pip install ragas langchain_openai python-dotenv"]}, {"cell_type": "code", "execution_count": 1, "id": "fb5deb25", "metadata": {}, "outputs": [], "source": ["# attach to the existing event loop when using jupyter notebooks\n", "import nest_asyncio\n", "import os\n", "import openai\n", "from dotenv import load_dotenv\n", "\n", "# Load environment variables from .env file\n", "load_dotenv()\n", "# IMPORTANT: Remember to create a .env variable containing: OPENAI_API_KEY=sk-xyz where xyz is your key\n", "\n", "# Access the API key from the environment variable\n", "api_key = os.environ.get(\"OPENAI_API_KEY\")\n", "\n", "# Initialize the OpenAI API client\n", "openai.api_key = api_key\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "markdown", "id": "842e32dc", "metadata": {}, "source": ["First lets load the dataset. We are going to build a generic QA system over the [NYC wikipedia page](https://en.wikipedia.org/wiki/New_York_City). Load the dataset and create the `VectorstoreIndex` and the `RetrievalQA` from it."]}, {"cell_type": "code", "execution_count": 2, "id": "4aa9a986", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/.pyenv/versions/ragas/lib/python3.10/site-packages/langchain/indexes/vectorstore.py:128: UserWarning: Using InMemoryVectorStore as the default vectorstore.This memory store won't persist data. You should explicitlyspecify a vectorstore when using VectorstoreIndexCreator\n", "  warnings.warn(\n"]}, {"ename": "ValidationError", "evalue": "1 validation error for VectorstoreIndexCreator\nembedding\n  Field required [type=missing, input_value={}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.9/v/missing", "output_type": "error", "traceback": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mValidationError\u001b[0m                           <PERSON><PERSON> (most recent call last)", "Cell \u001b[0;32mIn[2], line 7\u001b[0m\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mlangchain_openai\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m ChatOpenAI\n\u001b[1;32m      6\u001b[0m loader \u001b[38;5;241m=\u001b[39m TextLoader(\u001b[38;5;124m\"\u001b[39m\u001b[38;5;124m./nyc_wikipedia/nyc_text.txt\u001b[39m\u001b[38;5;124m\"\u001b[39m)\n\u001b[0;32m----> 7\u001b[0m index \u001b[38;5;241m=\u001b[39m \u001b[43mVectorstoreIndexCreator\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\u001b[38;5;241m.\u001b[39mfrom_loaders([loader])\n\u001b[1;32m     10\u001b[0m llm \u001b[38;5;241m=\u001b[39m ChatOpenAI(temperature\u001b[38;5;241m=\u001b[39m\u001b[38;5;241m0\u001b[39m)\n\u001b[1;32m     11\u001b[0m qa_chain \u001b[38;5;241m=\u001b[39m RetrievalQA\u001b[38;5;241m.\u001b[39mfrom_chain_type(\n\u001b[1;32m     12\u001b[0m     llm,\n\u001b[1;32m     13\u001b[0m     retriever\u001b[38;5;241m=\u001b[39mindex\u001b[38;5;241m.\u001b[39mvectorstore\u001b[38;5;241m.\u001b[39mas_retriever(),\n\u001b[1;32m     14\u001b[0m     return_source_documents\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mTrue\u001b[39;00m,\n\u001b[1;32m     15\u001b[0m )\n", "File \u001b[0;32m~/.pyenv/versions/ragas/lib/python3.10/site-packages/pydantic/main.py:212\u001b[0m, in \u001b[0;36mBaseModel.__init__\u001b[0;34m(self, **data)\u001b[0m\n\u001b[1;32m    210\u001b[0m \u001b[38;5;66;03m# `__tracebackhide__` tells pytest and some other tools to omit this function from tracebacks\u001b[39;00m\n\u001b[1;32m    211\u001b[0m __tracebackhide__ \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;01mTrue\u001b[39;00m\n\u001b[0;32m--> 212\u001b[0m validated_self \u001b[38;5;241m=\u001b[39m \u001b[38;5;28;43mself\u001b[39;49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43m__pydantic_validator__\u001b[49m\u001b[38;5;241;43m.\u001b[39;49m\u001b[43mvalidate_python\u001b[49m\u001b[43m(\u001b[49m\u001b[43mdata\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mself_instance\u001b[49m\u001b[38;5;241;43m=\u001b[39;49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[1;32m    213\u001b[0m \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m validated_self:\n\u001b[1;32m    214\u001b[0m     warnings\u001b[38;5;241m.\u001b[39mwarn(\n\u001b[1;32m    215\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mA custom validator is returning a value other than `self`.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m'\u001b[39m\n\u001b[1;32m    216\u001b[0m         \u001b[38;5;124m\"\u001b[39m\u001b[38;5;124mReturning anything other than `self` from a top level model validator isn\u001b[39m\u001b[38;5;124m'\u001b[39m\u001b[38;5;124mt supported when validating via `__init__`.\u001b[39m\u001b[38;5;130;01m\\n\u001b[39;00m\u001b[38;5;124m\"\u001b[39m\n\u001b[1;32m    217\u001b[0m         \u001b[38;5;124m'\u001b[39m\u001b[38;5;124mSee the `model_validator` docs (https://docs.pydantic.dev/latest/concepts/validators/#model-validators) for more details.\u001b[39m\u001b[38;5;124m'\u001b[39m,\n\u001b[1;32m    218\u001b[0m         category\u001b[38;5;241m=\u001b[39m\u001b[38;5;28;01mNone\u001b[39;00m,\n\u001b[1;32m    219\u001b[0m     )\n", "\u001b[0;31mValidationError\u001b[0m: 1 validation error for VectorstoreIndexCreator\nembedding\n  Field required [type=missing, input_value={}, input_type=dict]\n    For further information visit https://errors.pydantic.dev/2.9/v/missing"]}], "source": ["from langchain_community.document_loaders import TextLoader\n", "from langchain.indexes import VectorstoreIndexCreator\n", "from langchain.chains import RetrievalQA\n", "from langchain_openai import ChatOpenAI\n", "\n", "loader = TextLoader(\"./nyc_wikipedia/nyc_text.txt\")\n", "index = VectorstoreIndexCreator().from_loaders([loader])\n", "\n", "\n", "llm = ChatOpenAI(temperature=0)\n", "qa_chain = RetrievalQA.from_chain_type(\n", "    llm,\n", "    retriever=index.vectorstore.as_retriever(),\n", "    return_source_documents=True,\n", ")"]}, {"cell_type": "code", "execution_count": null, "id": "b0ebdf8d", "metadata": {}, "outputs": [], "source": ["# testing it out\n", "\n", "question = \"How did New York City get its name?\"\n", "result = qa_chain({\"query\": question})\n", "result[\"result\"]"]}, {"cell_type": "markdown", "id": "748787c1", "metadata": {}, "source": ["Now in order to evaluate the qa system we generated a few relevant questions. We've generated a few question for you but feel free to add any you want."]}, {"cell_type": "code", "execution_count": null, "id": "e67ce0e0", "metadata": {}, "outputs": [], "source": ["eval_questions = [\n", "    \"What is the population of New York City as of 2020?\",\n", "    \"Which borough of New York City has the highest population?\",\n", "    \"What is the economic significance of New York City?\",\n", "    \"How did New York City get its name?\",\n", "    \"What is the significance of the Statue of Liberty in New York City?\",\n", "]\n", "\n", "eval_answers = [\n", "    \"8,804,190\",\n", "    \"Brooklyn\",\n", "    \"New York City's economic significance is vast, as it serves as the global financial capital, housing Wall Street and major financial institutions. Its diverse economy spans technology, media, healthcare, education, and more, making it resilient to economic fluctuations. NYC is a hub for international business, attracting global companies, and boasts a large, skilled labor force. Its real estate market, tourism, cultural industries, and educational institutions further fuel its economic prowess. The city's transportation network and global influence amplify its impact on the world stage, solidifying its status as a vital economic player and cultural epicenter.\",\n", "    \"New York City got its name when it came under British control in 1664. King <PERSON> of England granted the lands to his brother, the <PERSON> of York, who named the city New York in his own honor.\",\n", "    \"The Statue of Liberty in New York City holds great significance as a symbol of the United States and its ideals of liberty and peace. It greeted millions of immigrants who arrived in the U.S. by ship in the late 19th and early 20th centuries, representing hope and freedom for those seeking a better life. It has since become an iconic landmark and a global symbol of cultural diversity and freedom.\",\n", "]\n", "\n", "examples = [\n", "    {\"query\": q, \"ground_truth\": [eval_answers[i]]}\n", "    for i, q in enumerate(eval_questions)\n", "]"]}, {"cell_type": "markdown", "id": "84b7e2c4", "metadata": {}, "source": ["## Introducing `RagasEvaluatorChain`\n", "\n", "`RagasEvaluator<PERSON><PERSON><PERSON>` creates a wrapper around the metrics ragas provides (documented [here](https://github.com/explodinggradients/ragas/blob/main/docs/concepts/metrics/index.md)), making it easier to run these evaluation with langchain and langsmith.\n", "\n", "The evaluator chain has the following APIs\n", "\n", "- `__call__()`: call the `RagasEvaluator<PERSON><PERSON>n` directly on the result of a QA chain.\n", "- `evaluate()`: evaluate on a list of examples (with the input queries) and predictions (outputs from the QA chain). \n", "- `evaluate_run()`: method implemented that is called by langsmith evaluators to evaluate langsmith datasets.\n", "\n", "lets see each of them in action to learn more."]}, {"cell_type": "code", "execution_count": null, "id": "8f89d719", "metadata": {}, "outputs": [], "source": ["result = qa_chain({\"query\": eval_questions[1]})\n", "result[\"result\"]"]}, {"cell_type": "code", "execution_count": null, "id": "81fa9c47", "metadata": {}, "outputs": [], "source": ["result = qa_chain(examples[4])\n", "result[\"result\"]"]}, {"cell_type": "code", "execution_count": null, "id": "1d9266d4", "metadata": {}, "outputs": [], "source": ["from ragas.langchain.evalchain import RagasEvaluatorChain\n", "from ragas.metrics import (\n", "    faithfulness,\n", "    answer_relevancy,\n", "    context_precision,\n", "    context_recall,\n", ")\n", "\n", "# create evaluation chains\n", "faithfulness_chain = RagasEvaluatorChain(metric=faithfulness)\n", "answer_rel_chain = RagasEvaluatorChain(metric=answer_relevancy)\n", "context_rel_chain = RagasEvaluatorChain(metric=context_precision)\n", "context_recall_chain = RagasEvaluatorChain(metric=context_recall)"]}, {"cell_type": "markdown", "id": "9fb95467", "metadata": {}, "source": ["1. `__call__()`\n", "\n", "Directly run the evaluation chain with the results from the QA chain. Do note that metrics like context_precision and faithfulness require the `source_documents` to be present."]}, {"cell_type": "code", "execution_count": null, "id": "1b574584", "metadata": {}, "outputs": [], "source": ["# Recheck the result that we are going to validate.\n", "result"]}, {"cell_type": "markdown", "id": "0a8d182f", "metadata": {}, "source": ["**Faithfulness**"]}, {"cell_type": "code", "execution_count": null, "id": "5ede32cd", "metadata": {}, "outputs": [], "source": ["eval_result = faithfulness_chain(result)\n", "eval_result[\"faithfulness_score\"]"]}, {"cell_type": "markdown", "id": "6a080160", "metadata": {}, "source": ["High faithfulness_score means that there are exact consistency between the source documents and the answer.\n", "\n", "You can check lower faithfulness scores by changing the result (answer from LLM) or source_documents to something else."]}, {"cell_type": "code", "execution_count": null, "id": "d46535f6", "metadata": {}, "outputs": [], "source": ["fake_result = result.copy()\n", "fake_result[\"result\"] = \"we are the champions\"\n", "eval_result = faithfulness_chain(fake_result)\n", "eval_result[\"faithfulness_score\"]"]}, {"cell_type": "markdown", "id": "3f3a66f8", "metadata": {}, "source": ["**Context Recall**"]}, {"cell_type": "code", "execution_count": null, "id": "94b5544e", "metadata": {}, "outputs": [], "source": ["eval_result = context_recall_chain(result)\n", "eval_result[\"context_recall_score\"]"]}, {"cell_type": "markdown", "id": "f6d624d4", "metadata": {}, "source": ["High context_recall_score means that the ground truth is present in the source documents.\n", "\n", "You can check lower context recall scores by changing the source_documents to something else."]}, {"cell_type": "code", "execution_count": null, "id": "8fc25156", "metadata": {}, "outputs": [], "source": ["from langchain.schema import Document\n", "\n", "fake_result = result.copy()\n", "fake_result[\"source_documents\"] = [Document(page_content=\"I love christmas\")]\n", "eval_result = context_recall_chain(fake_result)\n", "eval_result[\"context_recall_score\"]"]}, {"cell_type": "markdown", "id": "f11295b5", "metadata": {}, "source": ["2. `evaluate()`\n", "\n", "Evaluate a list of inputs/queries and the outputs/predictions from the QA chain."]}, {"cell_type": "code", "execution_count": null, "id": "1ce7bff1", "metadata": {}, "outputs": [], "source": ["# run the queries as a batch for efficiency\n", "predictions = qa_chain.batch(examples)\n", "\n", "# evaluate\n", "print(\"evaluating...\")\n", "r = faithfulness_chain.evaluate(examples, predictions)\n", "r"]}, {"cell_type": "code", "execution_count": null, "id": "55299f14", "metadata": {}, "outputs": [], "source": ["# evaluate context recall\n", "print(\"evaluating...\")\n", "r = context_recall_chain.evaluate(examples, predictions)\n", "r"]}, {"cell_type": "markdown", "id": "4cc71587", "metadata": {}, "source": ["## Evaluate with langsmith\n", "\n", "[<PERSON><PERSON>](https://docs.smith.langchain.com/) is a platform that helps to debug, test, evaluate and monitor chains and agents built on any LLM framework. It also seamlessly integrates with LangChain. \n", "\n", "Langsmith also has a tools to build a testing dataset and run evaluations against them and with `RagasEvaluator<PERSON>hain` you can use the ragas metrics for running langsmith evaluations as well. To know more about langsmith evaluations checkout the [quickstart](https://docs.smith.langchain.com/evaluation/quickstart).\n", "\n", "\n", "Lets start of creating the dataset with the NYC questions listed in `eval_questions`. Create a new langsmith dataset and upload the questions."]}, {"cell_type": "code", "execution_count": null, "id": "e75144c5", "metadata": {}, "outputs": [], "source": ["# dataset creation\n", "\n", "from langsmith import Client\n", "from langsmith.utils import LangSmithError\n", "\n", "client = Client()\n", "dataset_name = \"NYC test\"\n", "\n", "try:\n", "    # check if dataset exists\n", "    dataset = client.read_dataset(dataset_name=dataset_name)\n", "    print(\"using existing dataset: \", dataset.name)\n", "except LangSmithError:\n", "    # if not create a new one with the generated query examples\n", "    dataset = client.create_dataset(\n", "        dataset_name=dataset_name, description=\"NYC test dataset\"\n", "    )\n", "    for e in examples:\n", "        client.create_example(\n", "            inputs={\"query\": e[\"query\"]},\n", "            outputs={\"ground_truth\": e[\"ground_truth\"]},\n", "            dataset_id=dataset.id,\n", "        )\n", "\n", "    print(\"Created a new dataset: \", dataset.name)"]}, {"cell_type": "markdown", "id": "c0181dac", "metadata": {}, "source": ["![](../../_static/langsmith-dataset.png)\n", "\n", "As you can see the questions have been uploaded. Now you can run your QA chain against this test dataset and compare the results in the langchain platform. \n", "\n", "Before you call `run_on_dataset` you need a factory function which creates a new instance of the QA chain you want to test. This is so that the internal state is not reused when running against each example."]}, {"cell_type": "code", "execution_count": null, "id": "3a6decc6", "metadata": {}, "outputs": [], "source": ["# factory function that return a new qa chain\n", "def create_qa_chain(return_context=True):\n", "    qa_chain = RetrievalQA.from_chain_type(\n", "        llm,\n", "        retriever=index.vectorstore.as_retriever(),\n", "        return_source_documents=return_context,\n", "    )\n", "    return qa_chain"]}, {"cell_type": "markdown", "id": "470ddc97", "metadata": {}, "source": ["Now lets run the evaluation"]}, {"cell_type": "code", "execution_count": null, "id": "25f7992f", "metadata": {}, "outputs": [], "source": ["from langchain.smith import RunEvalConfig, run_on_dataset\n", "\n", "evaluation_config = RunEvalConfig(\n", "    custom_evaluators=[\n", "        faithfulness_chain,\n", "        answer_rel_chain,\n", "        context_rel_chain,\n", "        context_recall_chain,\n", "    ],\n", "    prediction_key=\"result\",\n", ")\n", "\n", "result = run_on_dataset(\n", "    client,\n", "    dataset_name,\n", "    create_qa_chain,\n", "    evaluation=evaluation_config,\n", "    input_mapper=lambda x: x,\n", ")"]}, {"cell_type": "markdown", "id": "f64bb0c4", "metadata": {}, "source": ["You can follow the link to open the result for the run in langsmith. Check out the scores for each example too\n", "\n", "![](../../_static/langsmith-evaluation.png)"]}, {"cell_type": "markdown", "id": "125857c9", "metadata": {}, "source": ["Now if you want to dive more into the reasons for the scores and how to improve them, click on any example and open the feedback tab. This will show you each scores.\n", "\n", "![](../../_static/langsmith-feedback.png)\n", "\n", "You can also see the curresponding `RagasEvaluator<PERSON><PERSON><PERSON>` trace too to figure out why ragas scored the way it did.\n", "\n", "![](../../_static/langsmith-ragas-chain-trace.png)"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}