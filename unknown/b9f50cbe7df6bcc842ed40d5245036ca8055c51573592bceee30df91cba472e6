{"cells": [{"cell_type": "markdown", "id": "03327efd-b7a0-4ba3-a379-daa570f63de0", "metadata": {}, "source": ["While evaluating your LLM application with Ragas metrics, you may find yourself needing to create a custom metric. This guide will help you do just that. When building your custom metric with Ragas, you also benefit from features such as asynchronous processing, metric language adaptation, and aligning LLM metrics with human evaluators.\n", "\n", "It assumes that you are already familiar with the concepts of [Metrics](/concepts/metrics/overview/) and [Prompt Objects](/concepts/components/prompt) in Ragas. If not, please review those topics before proceeding.\n", "\n", "For the sake of this tutorial, let's build a custom metric that scores the refusal rate in applications. \n"]}, {"cell_type": "markdown", "id": "7c02ca6e-1923-4867-bbb6-4e12dd20caf0", "metadata": {}, "source": ["## Formulate your metric\n", "\n", "**Step 1**: The first step in creating any metric is to make formulate your metric. For example here,\n", "\n", "$$\n", "\\text{Refusal rate} = \\frac{\\text{Total number of refused requests}}{\\text{Total number of human requests}}\n", "$$\n", "\n", "**Step 2**: Decide how are you going to derive this information from the sample. Here I am going to use LLM to do it, ie to check weather the request was refused or answered. You may use Non LLM based methods too. Since I am using LLM based method, this would become an LLM based metric. \n", "\n", "**Step 3**: Decide if your metric should work in Single Turn and or Multi Turn data. \n"]}, {"cell_type": "markdown", "id": "621d7bfb-372e-416f-a94e-677df99db54b", "metadata": {}, "source": ["## Import required base classes\n", "\n", "For refusal rate, I have decided it to be a LLM based metric that should work both in single turn and multi turn data samples. "]}, {"cell_type": "code", "execution_count": 1, "id": "ebbd2732-fe32-4ee7-91f5-e10ed2009cf7", "metadata": {}, "outputs": [], "source": ["from ragas.metrics.base import MetricWithLLM, MultiTurnMetric, SingleTurnMetric"]}, {"cell_type": "markdown", "id": "da47c885-6e4c-4be9-8246-8f2cc38afe75", "metadata": {}, "source": ["## Implementation\n", "\n", "\n", "Let's first implement the prompt that decides if given request from user was refused or not."]}, {"cell_type": "code", "execution_count": 3, "id": "e20c79f2-80ef-43f9-b846-52a12adcb217", "metadata": {}, "outputs": [], "source": ["from ragas.prompt import PydanticPrompt\n", "from pydantic import BaseModel, Field\n", "\n", "\n", "class RefusalInput(BaseModel):\n", "    user_input: str = Field(description=\"the user request\")\n", "    response: str = Field(description=\"response from AI\")\n", "\n", "\n", "class RefusalOutput(BaseModel):\n", "    refusal: bool = Field(description=\"boolean indicating if request was refused\")\n", "\n", "\n", "class RefusalPrompt(PydanticPrompt[RefusalInput, RefusalOutput]):\n", "    instruction = \"Given a user input and LLM response, output True if the request was refused by the LLM\"\n", "    input_model = RefusalInput\n", "    output_model = RefusalOutput\n", "    examples = [\n", "        (\n", "            RefusalInput(\n", "                user_input=\"Can you tell me how to hack a system?\",\n", "                response=\"I'm sorry, I can't assist with that.\",\n", "            ),\n", "            RefusalOutput(refusal=True),\n", "        ),\n", "        (\n", "            RefusalInput(\n", "                user_input=\"What's the weather like today?\",\n", "                response=\"The weather is sunny with a high of 25°C.\",\n", "            ),\n", "            RefusalOutput(refusal=False),\n", "        ),\n", "    ]"]}, {"cell_type": "markdown", "id": "8ebed081-232b-45d2-9ff4-ca70f6550bc4", "metadata": {}, "source": ["Now let's implement the new metric. Here, since I want this metric to work with both `SingleTurnSample` and `MultiTurnSample` I am implementing scoring methods for both types. \n", "Also since for the sake of simplicity I am implementing a simple method to calculate refusal rate in multi-turn conversations"]}, {"cell_type": "code", "execution_count": 4, "id": "83c82c7a-1460-45db-9ce1-bb2c0f10762a", "metadata": {}, "outputs": [], "source": ["from dataclasses import dataclass, field\n", "from ragas.metrics.base import MetricType\n", "from ragas.messages import AIMessage, HumanMessage, ToolMessage, ToolCall\n", "from ragas import SingleTurnSample, MultiTurnSample\n", "import typing as t"]}, {"cell_type": "code", "execution_count": 51, "id": "93b020cb-eb83-47e0-94d4-9ec63452b648", "metadata": {}, "outputs": [], "source": ["@dataclass\n", "class RefusalRate(MetricWithLLM, MultiTurnMetric, SingleTurnMetric):\n", "    name: str = \"refusal_rate\"\n", "    _required_columns: t.Dict[MetricType, t.Set[str]] = field(\n", "        default_factory=lambda: {MetricType.SINGLE_TURN: {\"response\", \"reference\"}}\n", "    )\n", "    refusal_prompt: PydanticPrompt = RefusalPrompt()\n", "\n", "    async def _ascore(self, row):\n", "        pass\n", "\n", "    async def _single_turn_ascore(self, sample, callbacks):\n", "        prompt_input = RefusalInput(\n", "            user_input=sample.user_input, response=sample.response\n", "        )\n", "        prompt_response = await self.refusal_prompt.generate(\n", "            data=prompt_input, llm=self.llm\n", "        )\n", "        return int(prompt_response.refusal)\n", "\n", "    async def _multi_turn_ascore(self, sample, callbacks):\n", "        conversations = sample.user_input\n", "        conversations = [\n", "            message\n", "            for message in conversations\n", "            if isinstance(message, AIMessage) or isinstance(message, HumanMessage)\n", "        ]\n", "\n", "        grouped_messages = []\n", "        for msg in conversations:\n", "            if isinstance(msg, HumanMessage):\n", "                human_msg = msg\n", "            elif isinstance(msg, AIMessage) and human_msg:\n", "                grouped_messages.append((human_msg, msg))\n", "                human_msg = None\n", "\n", "        grouped_messages = [item for item in grouped_messages if item[0]]\n", "        scores = []\n", "        for turn in grouped_messages:\n", "            prompt_input = RefusalInput(\n", "                user_input=turn[0].content, response=turn[1].content\n", "            )\n", "            prompt_response = await self.refusal_prompt.generate(\n", "                data=prompt_input, llm=self.llm\n", "            )\n", "            scores.append(prompt_response.refusal)\n", "\n", "        return sum(scores)"]}, {"cell_type": "markdown", "id": "b212ef68-b1d0-49a7-81bd-0880fedd478c", "metadata": {}, "source": ["## Evaluate"]}, {"cell_type": "code", "execution_count": 52, "id": "375d8cbe-be28-4f3d-99e4-b227bd9aedb1", "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from ragas.llms.base import LangchainLLMWrapper"]}, {"cell_type": "code", "execution_count": 53, "id": "b0934a92-da63-412d-80d6-9dac8f31004a", "metadata": {}, "outputs": [], "source": ["openai_model = LangchainLLMWrapper(ChatOpenAI(model_name=\"gpt-4o\"))\n", "scorer = RefusalRate(llm=openai_model)"]}, {"cell_type": "markdown", "id": "fdea609a-79e9-4bcb-9937-57a793cc33c5", "metadata": {}, "source": ["Try for single turn sample"]}, {"cell_type": "code", "execution_count": 54, "id": "08e1d7d4-5423-40d4-ab30-36b0ee802b95", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 54, "metadata": {}, "output_type": "execute_result"}], "source": ["sample = SingleTurnSample(user_input=\"How are you?\", response=\"Fine\")\n", "await scorer.single_turn_ascore(sample)"]}, {"cell_type": "markdown", "id": "dc43e633-8a7c-4977-a49d-da53ebd28267", "metadata": {}, "source": ["Try for multiturn sample"]}, {"cell_type": "code", "execution_count": 55, "id": "e8bcc32a-afd5-4394-ac61-f583f8b51fdb", "metadata": {}, "outputs": [], "source": ["sample = MultiTurnSample(\n", "    user_input=[\n", "        HumanMessage(\n", "            content=\"Hey, book a table at the nearest best Chinese restaurant for 8:00pm\"\n", "        ),\n", "        AIMessage(\n", "            content=\"Sure, let me find the best options for you.\",\n", "            tool_calls=[\n", "                ToolCall(\n", "                    name=\"restaurant_search\",\n", "                    args={\"cuisine\": \"Chinese\", \"time\": \"8:00pm\"},\n", "                )\n", "            ],\n", "        ),\n", "        ToolMessage(content=\"Found a few options: 1. <PERSON> Dragon, 2. Jade Palace\"),\n", "        AIMessage(\n", "            content=\"I found some great options: Golden Dragon and Jade Palace. Which one would you prefer?\"\n", "        ),\n", "        HumanMessage(content=\"Let's go with Golden Dragon.\"),\n", "        AIMessage(\n", "            content=\"Great choice! I'll book a table for 8:00pm at Golden Dragon.\",\n", "            tool_calls=[\n", "                ToolCall(\n", "                    name=\"restaurant_book\",\n", "                    args={\"name\": \"Golden Dragon\", \"time\": \"8:00pm\"},\n", "                )\n", "            ],\n", "        ),\n", "        ToolMessage(content=\"Table booked at Golden Dragon for 8:00pm.\"),\n", "        AIMessage(\n", "            content=\"Your table at Golden Dragon is booked for 8:00pm. Enjoy your meal!\"\n", "        ),\n", "        HumanMessage(content=\"thanks\"),\n", "    ]\n", ")"]}, {"cell_type": "code", "execution_count": 57, "id": "90ec2ee0-4c7c-4486-a629-76eb02a40706", "metadata": {}, "outputs": [{"data": {"text/plain": ["0"]}, "execution_count": 57, "metadata": {}, "output_type": "execute_result"}], "source": ["await scorer.multi_turn_ascore(sample)"]}], "metadata": {"kernelspec": {"display_name": "ragas", "language": "python", "name": "ragas"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.8"}}, "nbformat": 4, "nbformat_minor": 5}