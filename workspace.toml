[workspace]
# This file configures the workspace-wide tools and settings for the Ragas monorepo
# All code quality tools (ruff, black, pyright) inherit from these centralized settings
# Individual projects only override when absolutely necessary for package-specific needs

[tool.ruff]
select = ["E", "F", "I"]
ignore = ["E501"]  # Line length handled by formatter
line-length = 88
target-version = "py39"
exclude = ["*.ipynb"]  # Exclude Jupyter notebooks from linting

[tool.ruff.lint.isort]
# Import sorting configuration for the entire monorepo
known-first-party = ["ragas", "ragas_experimental"]
force-single-line = false
combine-as-imports = true

[tool.black]
line-length = 88
target-version = ["py39"]
include = '\.pyi?$'

[tool.pyright]
include = ["ragas/src/ragas", "experimental/ragas_experimental"]
excludeTypeshedPaths = ["@types/*"]
pythonVersion = "3.9"
pythonPlatform = "All"
typeCheckingMode = "basic"

[tool.pytest.ini_options]
addopts = "-n 0"
asyncio_default_fixture_loop_scope = "function"
testpaths = ["ragas/tests"]