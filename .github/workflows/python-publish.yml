# This workflow will upload Python Packages using Twine when a release is created
# For more information see: https://docs.github.com/en/actions/automating-builds-and-tests/building-and-testing-python#publishing-to-package-registries

# This workflow uses actions that are not certified by GitHub.
# They are provided by a third-party and are governed by
# separate terms of service, privacy policy, and support
# documentation.

name: Upload Python Packages

on:
  release:
    types: [published]

permissions:
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    environment: pypi-release
    strategy:
      matrix:
        package:
          - name: ragas
            directory: ragas
            token: PYPI_API_TOKEN
          - name: ragas_experimental
            directory: experimental
            token: PYPI_API_TOKEN_EXPERIMENTAL
    steps:
    - uses: actions/checkout@v3
      with:
        fetch-depth: 0
    - name: Set up Python
      uses: actions/setup-python@v3
      with:
        python-version: '3.x'
    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip
        pip install --upgrade setuptools setuptools_scm[toml] build 
    - name: get setuptools-scm version 
      run: python -m setuptools_scm
      working-directory: ${{ matrix.package.directory }}
    - name: Build package
      run: python -m build
      working-directory: ${{ matrix.package.directory }}
    - name: Publish package
      uses: pypa/gh-action-pypi-publish@release/v1
      with:
        password: ${{ secrets[matrix.package.token] }}
        packages-dir: ${{ matrix.package.directory }}/dist/
