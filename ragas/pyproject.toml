[project]
name = "ragas"
requires-python = ">=3.9"
dependencies = [
    "numpy",
    "datasets",
    "tiktoken",
    "langchain",
    "langchain-core",
    "langchain-community",
    "langchain_openai",
    "nest-asyncio",
    "appdirs",
    "pydantic>=2",
    "openai>1",
    "diskcache>=5.6.3",
]
dynamic = ["version", "readme"]

[project.optional-dependencies]
experimental = ["ragas-experimental"]
all = [
    "sentence-transformers",
    "transformers",
    "nltk",
    "rouge_score",
    "rapidfuzz",
    "pandas",
    "datacompy",
    "llama_index",
    "r2r",
    "ragas-experimental"
]
docs = [
    "mkdocs>=1.6.1",
    "mkdocs-material",
    "mkdocs-material[imaging]",
    "mkdocstrings[python]",
    "mkdocs-glightbox",
    "mkdocs-autorefs",
    "mkdocs-gen-files",
    "mkdocs-literate-nav",
    "mkdocs-section-index",
    "mkdocs-git-committers-plugin-2",
    "mkdocs-git-revision-date-localized-plugin",
]
dev = [
    "rich",
    "ruff",
    "black[jupyter]",
    "pyright",
    "llama_index",
    "notebook",
    "sphinx-autobuild",
    "sentence-transformers",
    "transformers",
    "fastembed",
    "graphene",
    "rouge_score",
    "nltk",
    "rapidfuzz",
    "pandas",
    "datacompy",
    "haystack-ai",
    "sacrebleu",
    "r2r",
    "pytest",
    "pytest-xdist[psutil]",
    "pytest-asyncio",
    "nbmake",
]
test = []
[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.dynamic]
readme = {file = ["README.md"], content-type = "text/markdown"}

# Ruff configuration is inherited from workspace.toml at the monorepo root

[build-system]
requires = ["setuptools>=64", "setuptools_scm>=8"]
build-backend = "setuptools.build_meta"

[tool.setuptools_scm]
# Path to version file relative to this pyproject.toml
version_file = "src/ragas/_version.py"
# Point to the repository root (one level up)
root = ".."

[tool.pytest.ini_options]
addopts = "-n 0"
asyncio_default_fixture_loop_scope = "function"
[pytest]
testpaths = ["tests"]

[dependency-groups]
dev = [
    "arize-phoenix>=6.1.0",
    "openinference-instrumentation-langchain>=0.1.29",
]
