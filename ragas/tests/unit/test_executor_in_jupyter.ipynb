{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import asyncio\n", "from random import random"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def echo(index: int):\n", "    await asyncio.sleep(0.1)\n", "    return index\n", "\n", "\n", "async def echo_random_latency(index: int):\n", "    await asyncio.sleep(random())\n", "    return index"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test Executor "]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ragas.executor import is_event_loop_running, as_completed"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["assert is_event_loop_running() is True, \"is_event_loop_running() returned False\""]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["async def _run():\n", "    results = []\n", "    for t in await as_completed([echo(1), echo(2), echo(3)], 3):\n", "        r = await t\n", "        results.append(r)\n", "    return results\n", "\n", "\n", "results = await _run()\n", "\n", "expected = [1, 2, 3]\n", "assert results == expected, f\"got: {results}, expected: {expected}\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Test Executor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ragas.executor import Executor"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test order of results when they should return in submission order\n", "executor = Executor(raise_exceptions=True)\n", "for i in range(10):\n", "    executor.submit(echo, i, name=f\"echo_{i}\")\n", "\n", "results = executor.results()\n", "assert results == list(range(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# test order of results when may return unordered\n", "executor = Executor(batch_size=None)\n", "\n", "# add jobs to the executor\n", "for i in range(10):\n", "    executor.submit(echo_random_latency, i, name=f\"echo_order_{i}\")\n", "\n", "# Act\n", "results = executor.results()\n", "# Assert\n", "assert results == list(range(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test output order; batching\n", "executor = Executor(batch_size=3)\n", "\n", "# add jobs to the executor\n", "for i in range(10):\n", "    executor.submit(echo_random_latency, i, name=f\"echo_order_{i}\")\n", "\n", "# Act\n", "results = executor.results()\n", "# Assert\n", "assert results == list(range(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test no progress\n", "executor = Executor(show_progress=False)\n", "\n", "# add jobs to the executor\n", "for i in range(10):\n", "    executor.submit(echo_random_latency, i, name=f\"echo_order_{i}\")\n", "\n", "# Act\n", "results = executor.results()\n", "# Assert\n", "assert results == list(range(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Test multiple submission sets\n", "executor = Executor(raise_exceptions=True)\n", "for i in range(1000):\n", "    executor.submit(asyncio.sleep, 0.01)\n", "\n", "results = executor.results()\n", "assert results, \"Results should be list of None\"\n", "\n", "for i in range(1000):\n", "    executor.submit(asyncio.sleep, 0.01)\n", "\n", "results = executor.results()\n", "assert results, \"Results should be list of None\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test Metric"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ragas.metrics.base import Metric\n", "\n", "\n", "class FakeMetric(Metric):\n", "    name = \"fake_metric\"\n", "    _required_columns = (\"user_input\", \"response\")\n", "\n", "    def init(self):\n", "        pass\n", "\n", "    async def _ascore(self, row, callbacks) -> float:\n", "        return 0\n", "\n", "\n", "fm = FakeMetric()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["score = fm.score({\"user_input\": \"a\", \"response\": \"b\"})\n", "assert score == 0"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Test run_async_tasks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ragas.async_utils import run_async_tasks"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# run tasks unbatched\n", "tasks = [echo_random_latency(i) for i in range(10)]\n", "results = run_async_tasks(tasks, batch_size=None, show_progress=True)\n", "# Assert\n", "assert sorted(results) == list(range(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# run tasks batched\n", "tasks = [echo_random_latency(i) for i in range(10)]\n", "results = run_async_tasks(tasks, batch_size=3, show_progress=True)\n", "# Assert\n", "assert sorted(results) == list(range(10))"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["# Test no progress\n", "tasks = [echo_random_latency(i) for i in range(10)]\n", "results = run_async_tasks(tasks, batch_size=3, show_progress=False)\n", "# Assert\n", "assert sorted(results) == list(range(10))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}