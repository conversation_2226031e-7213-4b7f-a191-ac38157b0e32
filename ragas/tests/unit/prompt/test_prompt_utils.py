from collections import namedtuple

import pytest
from pydantic import BaseModel

from ragas.prompt.utils import extract_json, get_all_strings, update_strings


class Category(BaseModel):
    category: str
    name: str = "good name"
    is_good: bool = True
    number: int = 1


class Categories(BaseModel):
    list_of_categories: list[Category]
    list_of_names: list[str] = ["good_name1", "good_name2", "good_name3"]


old_strings = ["old1", "old2", "old3"]
new_strings = ["new1", "new2", "new3"]

OurTestCase = namedtuple("OurTestCase", ["obj", "old_strings", "new_strings"])

test_cases = [
    OurTestCase(
        obj={
            "a": "old 1",
            "b": "old 2",
            "c": ["old 1", "old 2", "old 3"],
            "d": {"e": "old 2"},
        },
        old_strings=["old 1", "old 2", "old 1", "old 2", "old 3", "old 2"],
        new_strings=["old_1", "old_2", "old_1", "old_2", "old_3", "old_2"],
    ),
    OurTestCase(
        obj=Categories(
            list_of_categories=[
                Category(category="old 1", name="name old1"),
                Category(category="old 2", name="name old2"),
                Category(category="old 3", name="name old3"),
                Category(category="old 1", name="name old1"),
            ],
            list_of_names=["name 1", "name 2", "name 3"],
        ),
        old_strings=[
            "old 1",
            "name old1",
            "old 2",
            "name old2",
            "old 3",
            "name old3",
            "old 1",
            "name old1",
            "name 1",
            "name 2",
            "name 3",
        ],
        new_strings=[
            "old_1",
            "name old1",
            "old_2",
            "name old2",
            "old_3",
            "name old3",
            "old_1",
            "name old1",
            "name 1",
            "name 2",
            "name 3",
        ],
    ),
    OurTestCase(
        obj=[
            Category(category="old 1", is_good=True, number=1),
            Category(category="old 2", is_good=True, number=2),
            Category(category="old 3", is_good=True, number=3),
            Category(category="old 1", is_good=True, number=4),
        ],
        old_strings=[
            "old 1",
            "good name",
            "old 2",
            "good name",
            "old 3",
            "good name",
            "old 1",
            "good name",
        ],
        new_strings=[
            "old_1",
            "good_name",
            "old_2",
            "good_name",
            "old_3",
            "good_name",
            "old_1",
            "good_name",
        ],
    ),
]


@pytest.mark.parametrize(
    "obj, expected",
    [(test_case.obj, test_case.old_strings) for test_case in test_cases],
)
def test_get_all_strings(obj, expected):
    assert get_all_strings(obj) == expected


@pytest.mark.parametrize(
    "obj, old_strings, new_strings",
    [
        (test_case.obj, test_case.old_strings, test_case.new_strings)
        for test_case in test_cases
    ],
)
def test_update_strings(obj, old_strings, new_strings):
    updated_obj = update_strings(obj, old_strings, new_strings)

    assert get_all_strings(updated_obj) == new_strings
    assert get_all_strings(obj) == old_strings


class TestExtractJson:
    prefix = "Here's the generated abstract conceptual question in the requested JSON format: "
    suffix = "Would you like me to explain in more detail?"
    object = """{"key": "value"}"""
    array = """[1, 2, 3]"""
    nested = """{"outer": {"inner": [1, 2, 3]}}"""

    test_cases = [
        (object, object),
        (array, array),
        (nested, nested),
        (prefix + object, object),
        (object + suffix, object),
        (prefix + object + suffix, object),
        (prefix + array, array),
        (array + suffix, array),
        (prefix + array + suffix, array),
        (prefix + nested, nested),
        (nested + suffix, nested),
        (prefix + nested + suffix, nested),
        (object + array + nested, object),
        (nested + object + array, nested),
    ]

    @pytest.mark.parametrize("text, expected", test_cases)
    def test_extract_json(self, text, expected):
        assert extract_json(text) == expected

    def test_extract_empty_array(self):
        text = "Here is an empty array: [] and some text."
        expected = "[]"
        assert extract_json(text) == expected

    def test_extract_empty_object(self):
        text = "Here is an empty object: {} and more text."
        expected = "{}"
        assert extract_json(text) == expected

    def test_extract_incomplete_json(self):
        text = 'Not complete: {"key": "value", "array": [1, 2, 3'
        expected = 'Not complete: {"key": "value", "array": [1, 2, 3'
        assert extract_json(text) == expected

    def test_markdown_json(self):
        text = """
        ```python
        import json

        def modify_query(input_data):
            query = input_data["query"]
            style = input_data["style"]
            length = input_data["length"]

            if style == "Poor grammar":
                # Poor grammar modifications (simplified for brevity)
                query = query.replace("How", "how")
                query = query.replace("do", "does")
                query = query.replace("terms of", "in terms of")
                query = query.replace("and", "")

            if length == "long":
                # Long text modifications (simplified for brevity)
                query += "?"

            return {
                "text": query
            }

        input_data = {
            "query": "How can the provided commands be used to manage and troubleshoot namespaces in a Kubernetes environment?",
            "style": "Poor grammar",
            "length": "long"
        }

        output = modify_query(input_data)
        print(json.dumps(output, indent=4))
        ```

        Output:
        ```json
        {"text": "how does the provided commands be used to manage and troubleshoot namespaces in a Kubernetes environment?"}
        ```
        This Python function `modify_query` takes an input dictionary with query, style, and length as keys. It applies modifications based on the specified style (Poor grammar) and length (long). The modified query is then returned as a JSON object.

        Note: This implementation is simplified for brevity and may not cover all possible edge cases or nuances of natural language processing.
        """
        expected = """{"text": "how does the provided commands be used to manage and troubleshoot namespaces in a Kubernetes environment?"}"""
        assert extract_json(text) == expected
