"""
Ragas 适配器模块

将现有的 LLM 和 Embedding 客户端适配为 Ragas 兼容的接口
"""
import asyncio
from typing import List, Optional, Any, Dict
from dataclasses import dataclass

from ragas.llms.base import BaseRagasLLM
from ragas.embeddings.base import BaseRagasEmbeddings
from ragas.llms.prompt import PromptValue

from .config import config
from .llm_clients import call_llm, call_embedding


@dataclass
class RagasLLMAdapter(BaseRagasLLM):
    """Ragas LLM 适配器"""
    
    def __init__(self):
        super().__init__()
        self.model_name = config.model.llm_model_name
    
    def generate_text(
        self,
        prompt: PromptValue,
        n: int = 1,
        temperature: Optional[float] = None,
        stop: Optional[List[str]] = None,
        callbacks: Optional[Any] = None,
    ) -> List[str]:
        """同步生成文本"""
        # 运行异步方法
        return asyncio.run(self.agenerate_text(prompt, n, temperature, stop, callbacks))
    
    async def agenerate_text(
        self,
        prompt: PromptValue,
        n: int = 1,
        temperature: Optional[float] = None,
        stop: Optional[List[str]] = None,
        callbacks: Optional[Any] = None,
    ) -> List[str]:
        """异步生成文本"""
        # 获取提示文本
        if hasattr(prompt, 'to_string'):
            prompt_text = prompt.to_string()
        elif hasattr(prompt, 'text'):
            prompt_text = prompt.text
        else:
            prompt_text = str(prompt)
        
        # 准备调用参数
        kwargs = {}
        if temperature is not None:
            kwargs['temperature'] = temperature
        if stop is not None:
            kwargs['stop'] = stop
        
        # 生成多个响应
        results = []
        for _ in range(n):
            try:
                response = await call_llm(prompt_text, **kwargs)
                results.append(response)
            except Exception as e:
                print(f"LLM 调用失败: {e}")
                results.append("")
        
        return results
    
    def get_model_name(self) -> str:
        """获取模型名称"""
        return self.model_name


@dataclass 
class RagasEmbeddingAdapter(BaseRagasEmbeddings):
    """Ragas Embedding 适配器"""
    
    def __init__(self):
        super().__init__()
        self.model_name = config.model.embedding_model_name
        self.dimension = config.model.embedding_dimension
    
    def embed_query(self, text: str) -> List[float]:
        """同步嵌入查询"""
        return asyncio.run(self.aembed_query(text))
    
    async def aembed_query(self, text: str) -> List[float]:
        """异步嵌入查询"""
        try:
            embeddings = await call_embedding([text])
            return embeddings[0] if embeddings else []
        except Exception as e:
            print(f"Embedding 调用失败: {e}")
            return [0.0] * self.dimension
    
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """同步嵌入文档"""
        return asyncio.run(self.aembed_documents(texts))
    
    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """异步嵌入文档"""
        try:
            return await call_embedding(texts)
        except Exception as e:
            print(f"Embedding 调用失败: {e}")
            return [[0.0] * self.dimension] * len(texts)
    
    def get_model_name(self) -> str:
        """获取模型名称"""
        return self.model_name


# 创建全局实例
def create_ragas_llm() -> RagasLLMAdapter:
    """创建 Ragas LLM 适配器"""
    return RagasLLMAdapter()


def create_ragas_embeddings() -> RagasEmbeddingAdapter:
    """创建 Ragas Embedding 适配器"""
    return RagasEmbeddingAdapter()
