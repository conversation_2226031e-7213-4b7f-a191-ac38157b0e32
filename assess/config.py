"""
配置模块
"""
import os
from dataclasses import dataclass
from dotenv import load_dotenv

# 加载环境变量
load_dotenv(os.path.join(os.path.dirname(__file__), '.env'))


@dataclass
class ModelConfig:
    """模型配置"""
    # LLM配置
    llm_model_name: str = os.getenv('LLM_MODEL_NAME', 'qwen-turbo')
    llm_base_url: str = os.getenv('LLM_BASE_URL', 'http://192.168.50.31:3010/v1')
    llm_api_key: str = os.getenv('LLM_API_KEY', '')
    llm_max_tokens: int = int(os.getenv('LLM_MAX_TOKENS', '16384'))
    llm_temperature: float = float(os.getenv('LLM_TEMPERATURE', '0.1'))
    llm_timeout: int = int(os.getenv('LLM_TIMEOUT', '60'))
    
    # Embedding配置
    embedding_model_name: str = os.getenv('EMBEDDING_MODEL_NAME', 'nomic-embed-text:v1.5')
    embedding_api_key: str = os.getenv('EMBEDDING_API_KEY', '')
    embedding_base_url: str = os.getenv('EMBEDDING_BASE_URL', 'http://192.168.50.31:3010/v1')
    embedding_dimension: int = int(os.getenv('EMBEDDING_DIMENSION', '768'))
    embedding_batch_size: int = int(os.getenv('EMBEDDING_BATCH_SIZE', '10'))


@dataclass
class Config:
    """全局配置"""
    model: ModelConfig = ModelConfig()


# 全局配置实例
config = Config()
