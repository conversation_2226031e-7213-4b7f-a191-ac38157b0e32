# Testset Generation for Agents or Tool use cases

Evaluating agentic or tool use workflows can be challenging as it involves multiple steps and interactions. It can be especially hard to curate a test suite that covers all possible scenarios and edge cases. We are working on a set of tools to generate synthetic test data for evaluating agent workflows.

[Talk to founders](https://cal.com/shahul-ragas/30min) to work together on this and discover what's coming for upcoming releases.