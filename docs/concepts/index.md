# 📚 Core Concepts


<div class="grid cards" markdown>

-   :material-widgets:{ .lg .middle } [__Components Guides__](components/index.md)

    ---

    Discover the various components used within Ragas.
    
    Components like [Prompt Object](components/prompt.md), [Evaluation Dataset](components/eval_dataset.md) and [more..](components/index.md)


-   ::material-ruler-square:{ .lg .middle } [__Ragas Metrics__](metrics/index.md)

    ---

    Explore available metrics and understand how they work.

    Metrics for evaluating [RAG](metrics/available_metrics/index.md#retrieval-augmented-generation), [Agentic workflows](metrics/available_metrics/index.md#agents-or-tool-use-cases) and [more..](metrics/available_metrics/index.md#list-of-available-metrics).

-   :material-database-plus:{ .lg .middle } [__Test Data Generation__](test_data_generation/index.md)

    ---

    Generate high-quality datasets for comprehensive testing.

    Algorithms for synthesizing data to test [RAG](test_data_generation/rag.md), [Agentic workflows](test_data_generation/agents.md) 


-   :material-chart-box-outline:{ .lg .middle } [__Feedback Intelligence__](feedback/index.md)

    ---

    Leverage signals from production data to gain actionable insights.

    Learn about to leveraging implicit and explicit signals from production data.




</div>
