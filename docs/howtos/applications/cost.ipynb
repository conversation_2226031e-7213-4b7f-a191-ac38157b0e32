{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# How to estimate Cost and Usage of evaluations and testset generation\n", "\n", "When using LLMs for evaluation and test set generation, cost will be an important factor. Ragas provides you some tools to help you with that."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Implement `TokenUsageParser`\n", "\n", "By default Ragas does not calculate the usage of tokens for `evaluate()`. This is because langchain's LLMs do not always return information about token usage in a uniform way. So in order to get the usage data, we have to implement a `TokenUsageParser`. \n", "\n", "A `TokenUsageParser` is function that parses the `LLMResult` or `ChatResult` from langchain models `generate_prompt()` function and outputs `TokenUsage` which Ragas expects.\n", "\n", "For an example here is one that will parse OpenAI by using a parser we have defined."]}, {"cell_type": "code", "metadata": {}, "outputs": [], "source": ["from langchain_openai.chat_models import ChatOpenAI\n", "from langchain_core.prompt_values import StringPromptValue\n", "\n", "# lets import a parser for OpenAI\n", "from ragas.cost import get_token_usage_for_openai\n", "\n", "gpt4o = ChatOpenAI(model=\"gpt-4o\")\n", "p = StringPromptValue(text=\"hai there\")\n", "llm_result = gpt4o.generate_prompt([p])\n", "\n", "get_token_usage_for_openai(llm_result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can define your own or import parsers if they are defined. If you would like to suggest parser for LLM providers or contribute your own ones please check out this [issue](https://github.com/explodinggradients/ragas/issues/1151) 🙂."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Token Usage for Evaluations\n", "\n", "Let's use the `get_token_usage_for_openai` parser to calculate the token usage for an evaluation."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Repo card metadata block was not found. Setting CardData to empty.\n"]}], "source": ["from ragas import EvaluationDataset\n", "from datasets import load_dataset\n", "\n", "dataset = load_dataset(\"explodinggradients/amnesty_qa\", \"english_v3\")\n", "\n", "eval_dataset = EvaluationDataset.from_hf_dataset(dataset[\"eval\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can pass in the parser to the `evaluate()` function and the cost will be calculated and returned in the `Result` object."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "c9cf15f7bae64320b2bc389b98321a37", "version_major": 2, "version_minor": 0}, "text/plain": ["Evaluating:   0%|          | 0/20 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from ragas import evaluate\n", "from ragas.metrics import LLMContextRecall\n", "\n", "from ragas.cost import get_token_usage_for_openai\n", "\n", "result = evaluate(\n", "    eval_dataset,\n", "    metrics=[LLMContextRecall()],\n", "    llm=gpt4o,\n", "    token_usage_parser=get_token_usage_for_openai,\n", ")"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["TokenUsage(input_tokens=25097, output_tokens=3757, model='')"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["result.total_tokens()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can compute the cost for each run by passing in the cost per token to `Result.total_cost()` function.\n", "\n", "In this case GPT-4o costs $5 for 1M input tokens and $15 for 1M output tokens."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.1692900000000002"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["result.total_cost(cost_per_input_token=5 / 1e6, cost_per_output_token=15 / 1e6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Token Usage for Testset Generation\n", "\n", "You can use the same parser for testset generation but you need to pass in the `token_usage_parser` to the `generate()` function. For now it only calculates the cost for the generation process and not the cost for the transforms.\n", "\n", "For an example let's load an existing KnowledgeGraph and generate a testset. If you want to know more about how to generate a testset please check out the [testset generation](../../getstarted/rag_testset_generation.md#a-deeper-look)."]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/plain": ["KnowledgeGraph(nodes: 47, relationships: 109)"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["from ragas.testset.graph import KnowledgeGraph\n", "\n", "# loading an existing KnowledgeGraph\n", "# make sure to change the path to the location of the KnowledgeGraph file\n", "kg = KnowledgeGraph.load(\"../../../experiments/scratchpad_kg.json\")\n", "kg"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Choose your LLM\n", "\n", "--8<--\n", "choose_generator_llm.md\n", "--8<--"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ragas.testset import TestsetGenerator\n", "from ragas.llms import llm_factory\n", "\n", "tg = TestsetGenerator(llm=llm_factory(), knowledge_graph=kg)\n", "# generating a testset\n", "testset = tg.generate(testset_size=10, token_usage_parser=get_token_usage_for_openai)"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.20967000000000002"]}, "execution_count": 22, "metadata": {}, "output_type": "execute_result"}], "source": ["# total cost for the generation process\n", "testset.total_cost(cost_per_input_token=5 / 1e6, cost_per_output_token=15 / 1e6)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}