{"cells": [{"cell_type": "markdown", "id": "d2451aff", "metadata": {}, "source": ["# LlamaIndex\n", "\n", "[LlamaIndex](https://github.com/run-llama/llama_index) is a data framework for LLM applications to ingest, structure, and access private or domain-specific data. Makes it super easy to connect LLMs with your own data. But in order to figure out the best configuration for llamaIndex and your data you need a object measure of the performance. This is where ragas comes in. Ragas will help you evaluate your `QueryEngine` and gives you the confidence to tweak the configuration to get hightest score.\n", "\n", "This guide assumes you have familarity with the LlamaIndex framework."]}, {"cell_type": "markdown", "id": "ea0553ea", "metadata": {}, "source": ["## Building the Testset\n", "\n", "You will need an testset to evaluate your `QueryEngine` against. You can either build one yourself or use the [Testset Generator Module](../../getstarted/testset_generation.md) in Ragas to get started with a small synthetic one.\n", "\n", "Let's see how that works with Llamaindex"]}, {"cell_type": "markdown", "id": "096e5af0", "metadata": {}, "source": ["# load the documents"]}, {"cell_type": "code", "execution_count": 1, "id": "396085d5", "metadata": {}, "outputs": [], "source": ["from llama_index.core import SimpleDirectoryReader\n", "\n", "documents = SimpleDirectoryReader(\"./nyc_wikipedia\").load_data()"]}, {"cell_type": "markdown", "id": "012d81a1", "metadata": {}, "source": ["Now  lets init the `TestsetGenerator` object with the corresponding generator and critic llms"]}, {"cell_type": "code", "execution_count": 2, "id": "e2107b62", "metadata": {}, "outputs": [], "source": ["from ragas.testset import TestsetGenerator\n", "\n", "from llama_index.llms.openai import OpenAI\n", "from llama_index.embeddings.openai import OpenAIEmbedding\n", "\n", "# generator with openai models\n", "generator_llm = OpenAI(model=\"gpt-4o\")\n", "embeddings = OpenAIEmbedding(model=\"text-embedding-3-large\")\n", "\n", "generator = TestsetGenerator.from_llama_index(\n", "    llm=generator_llm,\n", "    embedding_model=embeddings,\n", ")"]}, {"cell_type": "markdown", "id": "f8d8d31c", "metadata": {}, "source": ["Now you are all set to generate the dataset"]}, {"cell_type": "code", "execution_count": null, "id": "fe03839d", "metadata": {}, "outputs": [], "source": ["# generate testset\n", "testset = generator.generate_with_llamaindex_docs(\n", "    documents,\n", "    testset_size=5,\n", ")"]}, {"cell_type": "code", "execution_count": 4, "id": "0b75a723", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_input</th>\n", "      <th>reference_contexts</th>\n", "      <th>reference</th>\n", "      <th>synthesizer_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Cud yu pleese explane the role of New York Cit...</td>\n", "      <td>[New York, often called New York City or NYC, ...</td>\n", "      <td>New York City serves as the geographical and d...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>So like, what was New York City called before ...</td>\n", "      <td>[History == === Early history === In the pre-C...</td>\n", "      <td>Before it was called New York, the area was kn...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>what happen in new york with slavery and how i...</td>\n", "      <td>[and rechristened it \"New Orange\" after Will<PERSON>...</td>\n", "      <td>In the early 18th century, New York became a c...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>What historical significance does Long Island ...</td>\n", "      <td>[&lt;1-hop&gt;\\n\\nHistory == === Early history === I...</td>\n", "      <td>Long Island holds historical significance in t...</td>\n", "      <td>multi_hop_specific_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>What role does the Staten Island Ferry play in...</td>\n", "      <td>[&lt;1-hop&gt;\\n\\nto start service in 2017; this wou...</td>\n", "      <td>The Staten Island Ferry plays a significant ro...</td>\n", "      <td>multi_hop_specific_query_synthesizer</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          user_input  \\\n", "0  Cud yu pleese explane the role of New York Cit...   \n", "1  So like, what was New York City called before ...   \n", "2  what happen in new york with slavery and how i...   \n", "3  What historical significance does Long Island ...   \n", "4  What role does the Staten Island Ferry play in...   \n", "\n", "                                  reference_contexts  \\\n", "0  [New York, often called New York City or NYC, ...   \n", "1  [History == === Early history === In the pre-C...   \n", "2  [and rechristened it \"New Orange\" after <PERSON><PERSON>...   \n", "3  [<1-hop>\\n\\nHistory == === Early history === I...   \n", "4  [<1-hop>\\n\\nto start service in 2017; this wou...   \n", "\n", "                                           reference  \\\n", "0  New York City serves as the geographical and d...   \n", "1  Before it was called New York, the area was kn...   \n", "2  In the early 18th century, New York became a c...   \n", "3  Long Island holds historical significance in t...   \n", "4  The Staten Island Ferry plays a significant ro...   \n", "\n", "                       synthesizer_name  \n", "0  single_hop_specifc_query_synthesizer  \n", "1  single_hop_specifc_query_synthesizer  \n", "2  single_hop_specifc_query_synthesizer  \n", "3  multi_hop_specific_query_synthesizer  \n", "4  multi_hop_specific_query_synthesizer  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df = testset.to_pandas()\n", "df.head()"]}, {"cell_type": "markdown", "id": "6107ea8b", "metadata": {}, "source": ["with a test dataset to test our `QueryEngine` lets now build one and evaluate it."]}, {"cell_type": "markdown", "id": "abaf6538", "metadata": {}, "source": ["## Building the `QueryEngine`\n", "\n", "To start lets build an `VectorStoreIndex` over the New York Citie's [wikipedia page](https://en.wikipedia.org/wiki/New_York_City) as an example and use ragas to evaluate it. \n", "\n", "Since we already loaded the dataset into `documents` lets use that."]}, {"cell_type": "code", "execution_count": 5, "id": "37c4a1cb", "metadata": {}, "outputs": [], "source": ["# build query engine\n", "from llama_index.core import VectorStoreIndex\n", "\n", "vector_index = VectorStoreIndex.from_documents(documents)\n", "\n", "query_engine = vector_index.as_query_engine()"]}, {"cell_type": "markdown", "id": "13d676c0", "metadata": {}, "source": ["Lets try an sample question from the generated testset to see if it is working"]}, {"cell_type": "code", "execution_count": 6, "id": "895d95b2", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Cud yu pleese explane the role of New York City within the Northeast megalopolis, and how it contributes to the cultural and economic vibrancy of the region?'"]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# convert it to pandas dataset\n", "df = testset.to_pandas()\n", "df[\"user_input\"][0]"]}, {"cell_type": "code", "execution_count": 7, "id": "a25026c2", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["New York City serves as a key hub within the Northeast megalopolis, playing a significant role in enhancing the cultural and economic vibrancy of the region. Its status as a global center of creativity, entrepreneurship, and cultural diversity contributes to the overall dynamism of the area. The city's renowned arts scene, including Broadway theatre and numerous cultural institutions, attracts artists and audiences from around the world, enriching the cultural landscape of the Northeast megalopolis. Economically, New York City's position as a leading financial and fintech center, home to major stock exchanges and a bustling real estate market, bolsters the region's economic strength and influence. Additionally, the city's diverse culinary scene, influenced by its immigrant history, adds to the cultural richness of the region, making New York City a vital component of the Northeast megalopolis's cultural and economic tapestry.\n"]}], "source": ["response_vector = query_engine.query(df[\"user_input\"][0])\n", "\n", "print(response_vector)"]}, {"cell_type": "markdown", "id": "b678501e", "metadata": {}, "source": ["## Evaluating the `QueryEngine`\n", "\n", "Now that we have a `QueryEngine` for the `VectorStoreIndex` we can use the llama_index integration Ragas has to evaluate it. \n", "\n", "In order to run an evaluation with Ragas and LlamaIndex you need 3 things\n", "\n", "1. LlamaIndex `QueryEngine`: what we will be evaluating\n", "2. Metrics: Ragas defines a set of metrics that can measure different aspects of the `QueryEngine`. The available metrics and their meaning can be found [here](https://docs.ragas.io/en/latest/concepts/metrics/available_metrics/)\n", "3. Questions: A list of questions that ragas will test the `QueryEngine` against. "]}, {"cell_type": "markdown", "id": "145109ad", "metadata": {}, "source": ["first lets generate the questions. Ideally you should use that you see in production so that the distribution of question with which we evaluate matches the distribution of questions seen in production. This ensures that the scores reflect the performance seen in production but to start off we'll be using a few example question."]}, {"cell_type": "markdown", "id": "843bddb8", "metadata": {}, "source": ["Now lets import the metrics we will be using to evaluate"]}, {"cell_type": "code", "execution_count": 8, "id": "9875132a", "metadata": {}, "outputs": [], "source": ["# import metrics\n", "from ragas.metrics import (\n", "    Faithfulness,\n", "    AnswerRelevancy,\n", "    ContextPrecision,\n", "    Con<PERSON><PERSON><PERSON><PERSON>,\n", ")\n", "\n", "# init metrics with evaluator LLM\n", "from ragas.llms import LlamaIndexLLMWrapper\n", "\n", "evaluator_llm = LlamaIndexLLMWrapper(OpenAI(model=\"gpt-4o\"))\n", "metrics = [\n", "    Faithfulness(llm=evaluator_llm),\n", "    AnswerRelevancy(llm=evaluator_llm),\n", "    ContextPrecision(llm=evaluator_llm),\n", "    ContextRecall(llm=evaluator_llm),\n", "]"]}, {"cell_type": "markdown", "id": "605e5d96", "metadata": {}, "source": ["the `evaluate()` function expects a dict of \"question\" and \"ground_truth\" for metrics. You can easily convert the `testset` to that format"]}, {"cell_type": "code", "execution_count": 9, "id": "4b2a81ed", "metadata": {}, "outputs": [{"data": {"text/plain": ["EvaluationDataset(features=['user_input', 'reference_contexts', 'reference'], len=6)"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["# convert to Ragas Evaluation Dataset\n", "ragas_dataset = testset.to_evaluation_dataset()\n", "ragas_dataset"]}, {"cell_type": "markdown", "id": "8ae4a2d1", "metadata": {}, "source": ["Finally lets run the evaluation"]}, {"cell_type": "code", "execution_count": null, "id": "05633cc2", "metadata": {}, "outputs": [], "source": ["from ragas.integrations.llama_index import evaluate\n", "\n", "result = evaluate(\n", "    query_engine=query_engine,\n", "    metrics=metrics,\n", "    dataset=ragas_dataset,\n", ")"]}, {"cell_type": "code", "execution_count": 11, "id": "f927a943", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'faithfulness': 0.7454, 'answer_relevancy': 0.9348, 'context_precision': 0.6667, 'context_recall': 0.4667}\n"]}], "source": ["# final scores\n", "print(result)"]}, {"cell_type": "markdown", "id": "878b6b82", "metadata": {}, "source": ["You can convert into a pandas dataframe to run more analysis on it."]}, {"cell_type": "code", "execution_count": 12, "id": "b96311e2", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_input</th>\n", "      <th>retrieved_contexts</th>\n", "      <th>reference_contexts</th>\n", "      <th>response</th>\n", "      <th>reference</th>\n", "      <th>faithfulness</th>\n", "      <th>answer_relevancy</th>\n", "      <th>context_precision</th>\n", "      <th>context_recall</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Cud yu pleese explane the role of New York Cit...</td>\n", "      <td>[and its ideals of liberty and peace. In the 2...</td>\n", "      <td>[New York, often called New York City or NYC, ...</td>\n", "      <td>New York City plays a significant role within ...</td>\n", "      <td>New York City serves as the geographical and d...</td>\n", "      <td>0.615385</td>\n", "      <td>0.918217</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>So like, what was New York City called before ...</td>\n", "      <td>[New York City is the headquarters of the glob...</td>\n", "      <td>[History == === Early history === In the pre-C...</td>\n", "      <td>New York City was named New Amsterdam before i...</td>\n", "      <td>Before it was called New York, the area was kn...</td>\n", "      <td>1.000000</td>\n", "      <td>0.967821</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>what happen in new york with slavery and how i...</td>\n", "      <td>[=== Province of New York and slavery ===\\n\\nI...</td>\n", "      <td>[and rechristened it \"New Orange\" after Will<PERSON>...</td>\n", "      <td>Slavery became a significant part of New York'...</td>\n", "      <td>In the early 18th century, New York became a c...</td>\n", "      <td>1.000000</td>\n", "      <td>0.919264</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>What historical significance does Long Island ...</td>\n", "      <td>[==== River crossings ====\\n\\nNew York City is...</td>\n", "      <td>[&lt;1-hop&gt;\\n\\nHistory == === Early history === I...</td>\n", "      <td>Long Island played a significant role in the e...</td>\n", "      <td>Long Island holds historical significance in t...</td>\n", "      <td>0.500000</td>\n", "      <td>0.931895</td>\n", "      <td>0.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>What role does the Staten Island Ferry play in...</td>\n", "      <td>[==== Buses ====\\n\\nNew York City's public bus...</td>\n", "      <td>[&lt;1-hop&gt;\\n\\nto start service in 2017; this wou...</td>\n", "      <td>The Staten Island Ferry serves as a vital mode...</td>\n", "      <td>The Staten Island Ferry plays a significant ro...</td>\n", "      <td>0.500000</td>\n", "      <td>0.936920</td>\n", "      <td>1.0</td>\n", "      <td>0.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>How does Central Park's role as a cultural and...</td>\n", "      <td>[==== State parks ====\\n\\nThere are seven stat...</td>\n", "      <td>[&lt;1-hop&gt;\\n\\nCity has over 28,000 acres (110 km...</td>\n", "      <td>Central Park's role as a cultural and historic...</td>\n", "      <td>Central Park, located in middle-upper Manhatta...</td>\n", "      <td>0.857143</td>\n", "      <td>0.934841</td>\n", "      <td>1.0</td>\n", "      <td>0.8</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          user_input  \\\n", "0  Cud yu pleese explane the role of New York Cit...   \n", "1  So like, what was New York City called before ...   \n", "2  what happen in new york with slavery and how i...   \n", "3  What historical significance does Long Island ...   \n", "4  What role does the Staten Island Ferry play in...   \n", "5  How does Central Park's role as a cultural and...   \n", "\n", "                                  retrieved_contexts  \\\n", "0  [and its ideals of liberty and peace. In the 2...   \n", "1  [New York City is the headquarters of the glob...   \n", "2  [=== Province of New York and slavery ===\\n\\nI...   \n", "3  [==== River crossings ====\\n\\nNew York City is...   \n", "4  [==== Buses ====\\n\\nNew York City's public bus...   \n", "5  [==== State parks ====\\n\\nThere are seven stat...   \n", "\n", "                                  reference_contexts  \\\n", "0  [New York, often called New York City or NYC, ...   \n", "1  [History == === Early history === In the pre-C...   \n", "2  [and rechristened it \"New Orange\" after <PERSON><PERSON>...   \n", "3  [<1-hop>\\n\\nHistory == === Early history === I...   \n", "4  [<1-hop>\\n\\nto start service in 2017; this wou...   \n", "5  [<1-hop>\\n\\nCity has over 28,000 acres (110 km...   \n", "\n", "                                            response  \\\n", "0  New York City plays a significant role within ...   \n", "1  New York City was named New Amsterdam before i...   \n", "2  Slavery became a significant part of New York'...   \n", "3  Long Island played a significant role in the e...   \n", "4  The Staten Island Ferry serves as a vital mode...   \n", "5  Central Park's role as a cultural and historic...   \n", "\n", "                                           reference  faithfulness  \\\n", "0  New York City serves as the geographical and d...      0.615385   \n", "1  Before it was called New York, the area was kn...      1.000000   \n", "2  In the early 18th century, New York became a c...      1.000000   \n", "3  Long Island holds historical significance in t...      0.500000   \n", "4  The Staten Island Ferry plays a significant ro...      0.500000   \n", "5  Central Park, located in middle-upper Manhatta...      0.857143   \n", "\n", "   answer_relevancy  context_precision  context_recall  \n", "0          0.918217                0.0             0.0  \n", "1          0.967821                1.0             1.0  \n", "2          0.919264                1.0             1.0  \n", "3          0.931895                0.0             0.0  \n", "4          0.936920                1.0             0.0  \n", "5          0.934841                1.0             0.8  "]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["result.to_pandas()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 5}