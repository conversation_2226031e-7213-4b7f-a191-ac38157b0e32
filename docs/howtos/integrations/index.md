# Integrations

Ragas is a framework and can be integrated with a host of different frameworks
and tools so that you can use Ragas with your own toolchain. If any tool you
want is not supported feel free to raise an [issue](https://github.com/explodinggradients/ragas/issues/new) and we'll be more than
happy to look into it 🙂


## Frameworks

- [Amazon Bedrock](./amazon_bedrock.md) - Amazon Bedrock is a managed framework for building, deploying, and scaling intelligent agents and integrated AI solutions; more information can be found [here](https://aws.amazon.com/bedrock/).
- [Haystack](./haystack.md) - Haystack is a LLM orchestration framework to build customizable, production-ready LLM applications, more information can be found [here](https://haystack.deepset.ai/).
- [Griptape](./griptape.md) - Griptape framework simplifies generative AI application development through flexible abstractions for LLMs, RAG, and more, additional information can be found [here](https://docs.griptape.ai/stable/griptape-framework/).
- [Langchain](./langchain.md) - Langchain is a framework for building LLM applications, more information can be found [here](https://www.langchain.com/).
- [LlamaIndex for RAG](./_llamaindex.md) - LlamaIndex is a framework for building RAG applications, more information can be found [here](https://www.llamaindex.ai/).
- [LlamaIndex for Agents](./llamaindex_agents.md) - LlamaIndex enables building intelligent, semi-autonomous agents, more information can be found [here](https://www.llamaindex.ai/).
- [LlamaStack](./llama_stack.md) – A unified framework by Meta for building and deploying generative AI apps across local, cloud, and mobile; [docs](https://llama-stack.readthedocs.io/en/latest/)
- [R2R](./r2r.md) - R2R is an all-in-one solution for AI Retrieval-Augmented Generation (RAG) with production-ready features, more information can be found [here](https://r2r-docs.sciphi.ai/introduction)
- [Swarm](./swarm_agent_evaluation.md) - Swarm is a framework for orchestrating multiple AI agents, more information can be found [here](https://github.com/openai/swarm).

## Tracing Tools

Tools that help you trace the LLM calls can be integrated with Ragas to get the traces of the evaluator LLMs.

-  [Arize Phoenix](./_arize.md) - Arize is a platform for observability and debugging of LLMs, more information can be found [here](https://phoenix.arize.com/).
- [Langsmith](./langsmith.md) - Langsmith is a platform for observability and debugging of LLMs from Langchain, more information can be found [here](https://www.langchain.com/langsmith).