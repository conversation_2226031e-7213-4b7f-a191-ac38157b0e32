{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Haystack Integration\n", "\n", "Haystack is a  LLM orchestration framework to build customizable, production-ready LLM applications. \n", "\n", "The underlying concept of Haystack is that all individual tasks, such as storing documents, retrieving relevant data, and generating responses, are handled by modular components like Document Stores, Retrievers, and Generators, which are seamlessly connected and orchestrated using Pipelines."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Overview\n", "\n", "In this tutorial, we will build a RAG pipeline using Haystack and evaluate it with Ragas. We’ll start by setting up the various components of the RAG pipeline, and for evaluations, we will initialize the RagasEvaluator component. Once the components are set up, we'll connect the components to form the complete pipeline. Later in the tutorial, we will explore how to perform evaluations using custom-defined metrics in Ragas."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Installing Dependencies"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%pip install ragas-haystack"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Getting the data"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["dataset = [\n", "    \"OpenAI is one of the most recognized names in the large language model space, known for its GPT series of models. These models excel at generating human-like text and performing tasks like creative writing, answering questions, and summarizing content. GPT-4, their latest release, has set benchmarks in understanding context and delivering detailed responses.\",\n", "    \"Anthropic is well-known for its <PERSON> series of language models, designed with a strong focus on safety and ethical AI behavior. <PERSON> is particularly praised for its ability to follow complex instructions and generate text that aligns closely with user intent.\",\n", "    \"DeepMind, a division of Google, is recognized for its cutting-edge Gemini models, which are integrated into various Google products like Bard and Workspace tools. These models are renowned for their conversational abilities and their capacity to handle complex, multi-turn dialogues.\",\n", "    \"Meta AI is best known for its LLaMA (Large Language Model Meta AI) series, which has been made open-source for researchers and developers. LLaMA models are praised for their ability to support innovation and experimentation due to their accessibility and strong performance.\",\n", "    \"Meta AI with it's LLaMA models aims to democratize AI development by making high-quality models available for free, fostering collaboration across industries. Their open-source approach has been a game-changer for researchers without access to expensive resources.\",\n", "    \"Microsoft’s Azure AI platform is famous for integrating OpenAI’s GPT models, enabling businesses to use these advanced models in a scalable and secure cloud environment. Azure AI powers applications like Copilot in Office 365, helping users draft emails, generate summaries, and more.\",\n", "    \"Amazon’s Bedrock platform is recognized for providing access to various language models, including its own models and third-party ones like Anthropic’s Claude and AI21’s Jurassic. Bedrock is especially valued for its flexibility, allowing users to choose models based on their specific needs.\",\n", "    \"Cohere is well-known for its language models tailored for business use, excelling in tasks like search, summarization, and customer support. Their models are recognized for being efficient, cost-effective, and easy to integrate into workflows.\",\n", "    \"AI21 Labs is famous for its Jurassic series of language models, which are highly versatile and capable of handling tasks like content creation and code generation. The Jurassic models stand out for their natural language understanding and ability to generate detailed and coherent responses.\",\n", "    \"In the rapidly advancing field of artificial intelligence, several companies have made significant contributions with their large language models. Notable players include OpenAI, known for its GPT Series (including GPT-4); Anthropic, which offers the Claude Series; Google DeepMind with its Gemini Models; Meta AI, recognized for its LLaMA Series; Microsoft Azure AI, which integrates OpenAI’s GPT Models; Amazon AWS (Bedrock), providing access to various models including Claude (Anthropic) and Jurassic (AI21 Labs); Cohere, which offers its own models tailored for business use; and AI21 Labs, known for its Jurassic Series. These companies are shaping the landscape of AI by providing powerful models with diverse capabilities.\",\n", "]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Initialize components for RAG pipeline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Initializing the DocumentStore"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["from haystack import Document\n", "from haystack.document_stores.in_memory import InMemoryDocumentStore\n", "\n", "document_store = InMemoryDocumentStore()\n", "docs = [Document(content=doc) for doc in dataset]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Initalize the Document and Text Embedder"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from haystack.components.embedders import OpenAITextEmbedder, OpenAIDocumentEmbedder\n", "\n", "document_embedder = OpenAIDocumentEmbedder(model=\"text-embedding-3-small\")\n", "text_embedder = OpenAITextEmbedder(model=\"text-embedding-3-small\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now we have our document store and the document embedder, using them we will fill populate out vector datastore."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Calculating embeddings: 1it [00:01,  1.74s/it]\n"]}, {"data": {"text/plain": ["10"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["docs_with_embeddings = document_embedder.run(docs)\n", "document_store.write_documents(docs_with_embeddings[\"documents\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Initialize the Retriever"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["from haystack.components.retrievers.in_memory import InMemoryEmbeddingRetriever\n", "\n", "retriever = InMemoryEmbeddingRetriever(document_store, top_k=2)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Define a Template Prompt"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from haystack.components.builders import ChatPromptBuilder\n", "from haystack.dataclasses import ChatMessage\n", "\n", "template = [\n", "    ChatMessage.from_user(\n", "        \"\"\"\n", "Given the following information, answer the question.\n", "\n", "Context:\n", "{% for document in documents %}\n", "    {{ document.content }}\n", "{% endfor %}\n", "\n", "Question: {{question}}\n", "Answer:\n", "\"\"\"\n", "    )\n", "]\n", "\n", "prompt_builder = ChatPromptBuilder(template=template)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Initialize a ChatGenerator"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["from haystack.components.generators.chat import OpenAIChatGenerator\n", "\n", "chat_generator = OpenAIChatGenerator(model=\"gpt-4o-mini\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Setting up the RagasEvaluator\n", "\n", "Pass all the Ragas metrics you want to use for evaluation, ensuring that all the necessary information to calculate each selected metric is provided.\n", "\n", "For example:\n", "\n", "- **AnswerRelevancy**: requires both the **query** and the **response**.\n", "- **ContextPrecision**: requires the **query**, **retrieved documents**, and the **reference**.\n", "- **Faithfulness**: requires the **query**, **retrieved documents**, and the **response**.\n", "\n", "Make sure to include all relevant data for each metric to ensure accurate evaluation."]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [], "source": ["from haystack_integrations.components.evaluators.ragas import RagasEvaluator\n", "\n", "from langchain_openai import ChatOpenAI\n", "from ragas.llms import LangchainLLMWrapper\n", "from ragas.metrics import AnswerRelevancy, ContextPrecision, Faithfulness\n", "\n", "llm = ChatOpenAI(model=\"gpt-4o-mini\")\n", "evaluator_llm = LangchainLLMWrapper(llm)\n", "\n", "ragas_evaluator = RagasEvaluator(\n", "    ragas_metrics=[AnswerRelevancy(), ContextPrecision(), Faithfulness()],\n", "    evaluator_llm=evaluator_llm,\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Building and Assembling the Pipeline"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Creating the Pipeline"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["from haystack import Pipeline\n", "\n", "rag_pipeline = Pipeline()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Adding the components"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [], "source": ["from haystack.components.builders import AnswerBuilder\n", "\n", "rag_pipeline.add_component(\"text_embedder\", text_embedder)\n", "rag_pipeline.add_component(\"retriever\", retriever)\n", "rag_pipeline.add_component(\"prompt_builder\", prompt_builder)\n", "rag_pipeline.add_component(\"llm\", chat_generator)\n", "rag_pipeline.add_component(\"answer_builder\", AnswerBuilder())\n", "rag_pipeline.add_component(\"ragas_evaluator\", ragas_evaluator)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Connecting the components"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["<haystack.core.pipeline.pipeline.Pipeline object at 0x14b20fad0>\n", "🚅 Components\n", "  - text_embedder: OpenAITextEmbedder\n", "  - retriever: <PERSON><PERSON><PERSON>oryEmbeddingRetriever\n", "  - prompt_builder: ChatPromptBuilder\n", "  - llm: OpenAIChatGenerator\n", "  - answer_builder: AnswerBuilder\n", "  - ragas_evaluator: RagasEvaluator\n", "🛤️ Connections\n", "  - text_embedder.embedding -> retriever.query_embedding (List[float])\n", "  - retriever.documents -> prompt_builder.documents (List[Document])\n", "  - retriever.documents -> answer_builder.documents (List[Document])\n", "  - retriever.documents -> ragas_evaluator.documents (List[Document])\n", "  - prompt_builder.prompt -> llm.messages (List[ChatMessage])\n", "  - llm.replies -> answer_builder.replies (List[ChatMessage])\n", "  - llm.replies -> ragas_evaluator.response (List[ChatMessage])"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["rag_pipeline.connect(\"text_embedder.embedding\", \"retriever.query_embedding\")\n", "rag_pipeline.connect(\"retriever\", \"prompt_builder\")\n", "rag_pipeline.connect(\"prompt_builder.prompt\", \"llm.messages\")\n", "rag_pipeline.connect(\"llm.replies\", \"answer_builder.replies\")\n", "rag_pipeline.connect(\"retriever\", \"answer_builder.documents\")\n", "rag_pipeline.connect(\"llm.replies\", \"answer_builder.replies\")\n", "rag_pipeline.connect(\"retriever\", \"answer_builder.documents\")\n", "rag_pipeline.connect(\"retriever\", \"ragas_evaluator.documents\")\n", "rag_pipeline.connect(\"llm.replies\", \"ragas_evaluator.response\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Running the Pipeline"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Evaluating: 100%|██████████| 3/3 [00:14<00:00,  4.72s/it]\n"]}, {"name": "stdout", "output_type": "stream", "text": ["Meta AI's LLaMA models stand out due to their open-source nature, which allows researchers and developers easy access to high-quality language models without the need for expensive resources. This accessibility fosters innovation and experimentation, enabling collaboration across various industries. Moreover, the strong performance of the LLaMA models further enhances their appeal, making them valuable tools for advancing AI development. \n", "\n", "{'answer_relevancy': 0.9782, 'context_precision': 1.0000, 'faithfulness': 1.0000}\n"]}], "source": ["question = \"What makes Meta AI’s LLaMA models stand out?\"\n", "\n", "reference = \"Meta AI’s LLaMA models stand out for being open-source, supporting innovation and experimentation due to their accessibility and strong performance.\"\n", "\n", "\n", "result = rag_pipeline.run(\n", "    {\n", "        \"text_embedder\": {\"text\": question},\n", "        \"prompt_builder\": {\"question\": question},\n", "        \"answer_builder\": {\"query\": question},\n", "        \"ragas_evaluator\": {\"query\": question, \"reference\": reference},\n", "        # Each metric expects a specific set of parameters as input. Refer to the\n", "        # Ragas class' documentation for more details.\n", "    }\n", ")\n", "\n", "print(result[\"answer_builder\"][\"answers\"][0].data, \"\\n\")\n", "print(result[\"ragas_evaluator\"][\"result\"])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Advance Usage\n", "\n", "Instead of using the default ragas metrics, you can change them to fit your needs or even create your own custom metrics. After that, you can pass these to the RagasEvaluator component. To learn more about how to customize ragas metrics, check out the [docs](https://docs.ragas.io/en/stable/howtos/customizations/)."]}, {"cell_type": "markdown", "metadata": {}, "source": ["In the example below, we will define two custom Ragas metrics:\n", "\n", "1. **SportsRelevanceMetric**: This metric evaluates whether a question and its response are related to sports.\n", "2. **AnswerQualityMetric**: This metric measures how well the response provided by the LLM answers the user's question."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Evaluating: 100%|██████████| 2/2 [00:01<00:00,  1.62it/s]\n"]}, {"data": {"text/plain": ["{'sports_relevance_metric': 1.0000, 'domain_specific_rubrics': 3.0000}"]}, "execution_count": 16, "metadata": {}, "output_type": "execute_result"}], "source": ["from ragas.metrics import RubricsScore, AspectCritic\n", "\n", "SportsRelevanceMetric = AspectCritic(\n", "    name=\"sports_relevance_metric\",\n", "    definition=\"Were the question and response related to sports?\",\n", "    llm=evaluator_llm,\n", ")\n", "\n", "rubrics = {\n", "    \"score1_description\": \"The response does not answer the user input.\",\n", "    \"score2_description\": \"The response partially answers the user input.\",\n", "    \"score3_description\": \"The response fully answer the user input\",\n", "}\n", "\n", "evaluator = RagasEvaluator(\n", "    ragas_metrics=[\n", "        SportsRelevanceMetric,\n", "        RubricsScore(llm=evaluator_llm, rubrics=rubrics),\n", "    ],\n", "    evaluator_llm=evaluator_llm,\n", ")\n", "\n", "output = evaluator.run(\n", "    query=\"Which is the most popular global sport?\",\n", "    documents=[\n", "        \"Football is undoubtedly the world's most popular sport with\"\n", "        \" major events like the FIFA World Cup and sports personalities\"\n", "        \" like <PERSON><PERSON> and <PERSON><PERSON>, drawing a followership of more than 4\"\n", "        \" billion people.\"\n", "    ],\n", "    response=\"Football is the most popular sport with around 4 billion\"\n", "    \" followers worldwide\",\n", ")\n", "\n", "output[\"result\"]"]}], "metadata": {"kernelspec": {"display_name": "tempo", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}