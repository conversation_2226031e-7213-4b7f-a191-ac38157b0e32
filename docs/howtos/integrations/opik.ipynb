{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Comet Opik\n", "\n", "In this notebook, we will showcase how to use Opik with Ragas for monitoring and evaluation of RAG (Retrieval-Augmented Generation) pipelines.\n", "\n", "There are two main ways to use Opik with Ragas:\n", "\n", "1. Using Ragas metrics to score traces\n", "2. Using the Ragas `evaluate` function to score a dataset\n", "\n", "<center><img src=\"https://raw.githubusercontent.com/comet-ml/opik/main/apps/opik-documentation/documentation/static/img/opik-project-dashboard.png\" alt=\"Comet Opik project dashboard screenshot with list of traces and spans\" width=\"600\" style=\"border: 0.5px solid #ddd;\"/></center>\n", "\n", "## Setup\n", "\n", "[Comet](https://www.comet.com/site?utm_medium=docs&utm_source=ragas&utm_campaign=opik) provides a hosted version of the Opik platform, [simply create an account](https://www.comet.com/signup?from=llm&utm_medium=docs&utm_source=ragas&utm_campaign=opik) and grab you API Key.\n", "\n", "> You can also run the Opik platform locally, see the [installation guide](https://www.comet.com/docs/opik/self-host/self_hosting_opik?utm_medium=docs&utm_source=ragas&utm_campaign=opik/) for more information."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import os\n", "import getpass\n", "\n", "os.environ[\"OPIK_API_KEY\"] = getpass.getpass(\"Opik API Key: \")\n", "os.environ[\"OPIK_WORKSPACE\"] = input(\n", "    \"Comet workspace (often the same as your username): \"\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you are running the Opik platform locally, simply set:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# import os\n", "# os.environ[\"OPIK_URL_OVERRIDE\"] = \"http://localhost:5173/api\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Preparing our environment\n", "\n", "First, we will install the necessary libraries, configure the OpenAI API key and create a new Opik dataset."]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["%pip install opik --quiet\n", "\n", "import os\n", "import getpass\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = getpass.getpass(\"Enter your OpenAI API key: \")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["\n", "## Integrating Opik with Ra<PERSON>\n", "\n", "### Using Ragas metrics to score traces\n", "\n", "Ragas provides a set of metrics that can be used to evaluate the quality of a RAG pipeline, including but not limited to: `answer_relevancy`, `answer_similarity`, `answer_correctness`, `context_precision`, `context_recall`, `context_entity_recall`, `summarization_score`. You can find a full list of metrics in the [Ragas documentation](https://docs.ragas.io/en/latest/references/metrics.html#).\n", "\n", "These metrics can be computed on the fly and logged to traces or spans in Opik. For this example, we will start by creating a simple RAG pipeline and then scoring it using the `answer_relevancy` metric.\n", "\n", "#### Create the Ragas metric\n", "\n", "In order to use the Ragas metric without using the `evaluate` function, you need to initialize the metric with a `RunConfig` object and an LLM provider. For this example, we will use LangChain as the LLM provider with the Opik tracer enabled.\n", "\n", "We will first start by initializing the Ragas metric:"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# Import the metric\n", "from ragas.metrics import AnswerRelevancy\n", "\n", "# Import some additional dependencies\n", "from langchain_openai.chat_models import ChatOpenAI\n", "from langchain_openai.embeddings import OpenAIEmbeddings\n", "from ragas.llms import LangchainLLMWrapper\n", "from ragas.embeddings import LangchainEmbeddingsWrapper\n", "\n", "# Initialize the Ragas metric\n", "llm = LangchainLLMWrapper(ChatOpenAI())\n", "emb = LangchainEmbeddingsWrapper(OpenAIEmbeddings())\n", "\n", "answer_relevancy_metric = AnswerRelevancy(llm=llm, embeddings=emb)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Once the metric is initialized, you can use it to score a sample question. Given that the metric scoring is done asynchronously, you need to use the `asyncio` library to run the scoring function."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["# Run this cell first if you are running this in a Jupy<PERSON> notebook\n", "import nest_asyncio\n", "\n", "nest_asyncio.apply()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Answer Relevancy score: 1.0\n"]}], "source": ["import asyncio\n", "from ragas.integrations.opik import OpikTracer\n", "from ragas.dataset_schema import SingleTurnSample\n", "\n", "\n", "# Define the scoring function\n", "def compute_metric(metric, row):\n", "    row = SingleTurnSample(**row)\n", "\n", "    opik_tracer = OpikTracer()\n", "\n", "    async def get_score(opik_tracer, metric, row):\n", "        score = await metric.single_turn_ascore(row, callbacks=[OpikTracer()])\n", "        return score\n", "\n", "    # Run the async function using the current event loop\n", "    loop = asyncio.get_event_loop()\n", "\n", "    result = loop.run_until_complete(get_score(opik_tracer, metric, row))\n", "    return result\n", "\n", "\n", "# Score a simple example\n", "row = {\n", "    \"user_input\": \"What is the capital of France?\",\n", "    \"response\": \"Paris\",\n", "    \"retrieved_contexts\": [\"Paris is the capital of France.\", \"Paris is in France.\"],\n", "}\n", "\n", "score = compute_metric(answer_relevancy_metric, row)\n", "print(\"Answer Relevancy score:\", score)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["If you now navigate to Opik, you will be able to see that a new trace has been created in the `Default Project` project.\n", "\n", "#### Score traces\n", "\n", "You can score traces by using the `update_current_trace` function to get the current trace and passing the feedback scores to that function.\n", "\n", "The advantage of this approach is that the scoring span is added to the trace allowing for a more fine-grained analysis of the RAG pipeline. It will however run the Ragas metric calculation synchronously and so might not be suitable for production use-cases."]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["'Paris'"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["from opik import track\n", "from opik.opik_context import update_current_trace\n", "\n", "\n", "@track\n", "def retrieve_contexts(question):\n", "    # Define the retrieval function, in this case we will hard code the contexts\n", "    return [\"Paris is the capital of France.\", \"Paris is in France.\"]\n", "\n", "\n", "@track\n", "def answer_question(question, contexts):\n", "    # Define the answer function, in this case we will hard code the answer\n", "    return \"Paris\"\n", "\n", "\n", "@track(name=\"Compute Ragas metric score\", capture_input=False)\n", "def compute_rag_score(answer_relevancy_metric, question, answer, contexts):\n", "    # Define the score function\n", "    row = {\"user_input\": question, \"response\": answer, \"retrieved_contexts\": contexts}\n", "    score = compute_metric(answer_relevancy_metric, row)\n", "    return score\n", "\n", "\n", "@track\n", "def rag_pipeline(question):\n", "    # Define the pipeline\n", "    contexts = retrieve_contexts(question)\n", "    answer = answer_question(question, contexts)\n", "\n", "    score = compute_rag_score(answer_relevancy_metric, question, answer, contexts)\n", "    update_current_trace(\n", "        feedback_scores=[{\"name\": \"answer_relevancy\", \"value\": round(score, 4)}]\n", "    )\n", "\n", "    return answer\n", "\n", "\n", "rag_pipeline(\"What is the capital of France?\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#### Evaluating datasets\n", "\n", "If you looking at evaluating a dataset, you can use the Ragas `evaluate` function. When using this function, the Ragas library will compute the metrics on all the rows of the dataset and return a summary of the results.\n", "\n", "You can use the OpikTracer callback to log the results of the evaluation to the Opik platform. For this we will configure the OpikTracer"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"application/vnd.jupyter.widget-view+json": {"model_id": "07abcf96a39b4fd183756d5dc3b617c9", "version_major": 2, "version_minor": 0}, "text/plain": ["Evaluating:   0%|          | 0/6 [00:00<?, ?it/s]"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["{'context_precision': 1.0000, 'faithfulness': 0.7375, 'answer_relevancy': 0.9889}\n"]}], "source": ["from datasets import load_dataset\n", "from ragas.metrics import context_precision, answer_relevancy, faithfulness\n", "from ragas import evaluate\n", "\n", "fiqa_eval = load_dataset(\"explodinggradients/fiqa\", \"ragas_eval\")\n", "\n", "# Reformat the dataset to match the schema expected by the Ragas evaluate function\n", "dataset = fiqa_eval[\"baseline\"].select(range(3))\n", "\n", "dataset = dataset.map(\n", "    lambda x: {\n", "        \"user_input\": x[\"question\"],\n", "        \"reference\": x[\"ground_truths\"][0],\n", "        \"retrieved_contexts\": x[\"contexts\"],\n", "    }\n", ")\n", "\n", "opik_tracer_eval = OpikTracer(tags=[\"ragas_eval\"], metadata={\"evaluation_run\": True})\n", "\n", "result = evaluate(\n", "    dataset,\n", "    metrics=[context_precision, faithfulness, answer_relevancy],\n", "    callbacks=[opik_tracer_eval],\n", ")\n", "\n", "print(result)"]}], "metadata": {"kernelspec": {"display_name": "py312_llm_eval", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.4"}}, "nbformat": 4, "nbformat_minor": 2}