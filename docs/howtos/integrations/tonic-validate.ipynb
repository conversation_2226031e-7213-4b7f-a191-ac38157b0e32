{"cells": [{"attachments": {}, "cell_type": "markdown", "id": "bbac63ad-ccc7-4968-8676-280489a9073c", "metadata": {}, "source": ["# Tonic Validate\n", "## [<PERSON><PERSON>](https://tonic.ai/validate): Visualize Ragas Scores \n", "\n", "<center><img src=\"https://uploads-ssl.webflow.com/62e28cf08913e81176ba2c39/65e77bcde4a7dbf5d853d319_tonic_validate_ragas_screenshot.png\" alt=\"Tonic Validate Screenshot with list of projects and example graphs\" width=\"600\"/></center>\n", "\n", "Validate makes it easy to understand the performance of your RAG or LLM application by visualizing and tracking over time the scores generated by Ragas.  If you are already using Ragas today getting started is as easy as adding two additional lines of code into your python project.\n", "\n", "## Getting Started\n", "\n", "First create a [free validate account](https://validate.tonic.ai/signup).  Once logged in, you'll need to create a new project.  A project is typically associated to a single RAG or LLM application you wish to evaluate with Ragas.  Once you've given your project a name you'll be taken to the project's new home page.\n", "\n", "To begin sending scores to Tonic Validate you'll need to install the tonic-ragas-logger package which is used to ship scores.\n", "\n", "```bash\n", "pip install tonic-ragas-logger\n", "```\n", "\n", "Now, in your existing python project you can add the below two lines of code to wherever you are running Ragas.  This code will take the ```scores``` generated by Ragas' ```evaluate()``` function and ship the results to Tonic Validate.  The API Key and Project ID referenced below are both available form your newly created project's home page.\n", "\n", "```python\n", "validate_api = RagasValidateApi(\"<Validate API Key>\")\n", "validate_api.upload_results(\"<Project ID>\", scores)\n", "```\n", "\n", "As you begin sending scores to Validate you'll see Graphs being generated and 'Runs' being created.  A run is a collection of scores computed from a single call to ```evaluate()```.  You can see how average scores change over time or dig into a specific run to see how individual questions performed.\n", "<br/>\n", "<br/>\n", "\n", "<center><img src=\"https://uploads-ssl.webflow.com/62e28cf08913e81176ba2c39/65e77bcd0ce60786fccba1b0_tonic_validate_ragas_gif.gif\n", "\" width=\"900\"/></center>\n", "\n", "\n", "\n", "## Reaching out 👋\n", "If you have any questions or feedback for our UI the easiest way to get in touch is to file a GitHub issue on our repository where we maintain [tonic-validate](https://github.com/tonicai/tonic_validate), our own open source evaluation framework."]}, {"cell_type": "markdown", "id": "12c32e5a", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.8"}}, "nbformat": 4, "nbformat_minor": 5}