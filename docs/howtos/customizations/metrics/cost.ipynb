{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Understand Cost and Usage of Operations\n", "\n", "When using LLMs for evaluation and test set generation, cost will be an important factor. Ragas provides you some tools to help you with that."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Understanding `TokenUsageParser`\n", "\n", "By default Ragas does not calculate the usage of tokens for `evaluate()`. This is because langchain's LLMs do not always return information about token usage in a uniform way. So in order to get the usage data, we have to implement a `TokenUsageParser`. \n", "\n", "A `TokenUsageParser` is function that parses the `LLMResult` or `ChatResult` from langchain models `generate_prompt()` function and outputs `TokenUsage` which Ragas expects.\n", "\n", "For an example here is one that will parse OpenAI by using a parser we have defined."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "os.environ[\"OPENAI_API_KEY\"] = \"your-api-key\""]}, {"cell_type": "code", "metadata": {}, "outputs": [], "source": ["from langchain_openai.chat_models import ChatOpenAI\n", "from langchain_core.prompt_values import StringPromptValue\n", "\n", "# lets import a parser for OpenAI\n", "from ragas.cost import get_token_usage_for_openai\n", "\n", "gpt4o = ChatOpenAI(model=\"gpt-4o\")\n", "p = StringPromptValue(text=\"hai there\")\n", "llm_result = gpt4o.generate_prompt([p])\n", "\n", "get_token_usage_for_openai(llm_result)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can define your own or import parsers if they are defined. If you would like to suggest parser for LLM providers or contribute your own ones please check out this [issue](https://github.com/explodinggradients/ragas/issues/1151) 🙂.\n", "\n", "You can use it for evaluations as so. Using example from [get started](get-started-evaluation) here."]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Repo card metadata block was not found. Setting CardData to empty.\n"]}], "source": ["from datasets import load_dataset\n", "from ragas import EvaluationDataset\n", "from ragas.metrics._aspect_critic import AspectCriticWithReference\n", "\n", "dataset = load_dataset(\"explodinggradients/amnesty_qa\", \"english_v3\")\n", "\n", "\n", "eval_dataset = EvaluationDataset.from_hf_dataset(dataset[\"eval\"])\n", "\n", "metric = AspectCriticWithReference(\n", "    name=\"answer_correctness\",\n", "    definition=\"is the response correct compared to reference\",\n", ")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Evaluating: 100%|██████████| 5/5 [00:01<00:00,  2.81it/s]\n"]}], "source": ["from ragas import evaluate\n", "from ragas.cost import get_token_usage_for_openai\n", "\n", "results = evaluate(\n", "    eval_dataset[:5],\n", "    metrics=[metric],\n", "    llm=gpt4o,\n", "    token_usage_parser=get_token_usage_for_openai,\n", ")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["TokenUsage(input_tokens=5463, output_tokens=355, model='')"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["results.total_tokens()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["You can compute the cost for each run by passing in the cost per token to `Result.total_cost()` function.\n", "\n", "In this case GPT-4o costs $5 for 1M input tokens and $15 for 1M output tokens."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.03264"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["results.total_cost(cost_per_input_token=5 / 1e6, cost_per_output_token=15 / 1e6)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 2}