{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Synthetic test generation from multi-lingual and cross-lingual corpus\n", "\n", "In this notebook, you'll learn how to adapt synthetic test data generation to multi-lingual (non english) and cross-lingual settings. For the sake of this tutorial, I am generating queries in Spanish from Spanish wikipedia articles. "]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Download and Load corpus"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cloning into 'Sample_non_english_corpus'...\n", "remote: Enumerating objects: 12, done.\u001b[K\n", "remote: Counting objects: 100% (8/8), done.\u001b[K\n", "remote: Compressing objects: 100% (8/8), done.\u001b[K\n", "remote: Total 12 (delta 0), reused 0 (delta 0), pack-reused 4 (from 1)\u001b[K\n", "Unpacking objects: 100% (12/12), 11.43 KiB | 780.00 KiB/s, done.\n"]}], "source": ["! git clone https://huggingface.co/datasets/explodinggradients/Sample_non_english_corpus"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/homebrew/Caskroom/miniforge/base/envs/ragas/lib/python3.9/site-packages/requests/__init__.py:102: RequestsDependencyWarning: urllib3 (1.26.20) or chardet (5.2.0)/charset_normalizer (None) doesn't match a supported version!\n", "  warnings.warn(\"urllib3 ({}) or chardet ({})/charset_normalizer ({}) doesn't match a supported \"\n"]}], "source": ["from langchain_community.document_loaders import DirectoryLoader\n", "\n", "\n", "path = \"Sample_non_english_corpus/\"\n", "loader = DirectoryLoader(path, glob=\"**/*.txt\")\n", "docs = loader.load()"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["6"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["len(docs)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Intialize required models"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/homebrew/Caskroom/miniforge/base/envs/ragas/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from ragas.llms import LangchainLLMWrapper\n", "from ragas.embeddings import LangchainEmbeddingsWrapper\n", "from langchain_openai import ChatOpenAI\n", "from langchain_openai import OpenAIEmbeddings\n", "\n", "generator_llm = LangchainLLMWrapper(ChatOpenAI(model=\"gpt-4o-mini\"))\n", "generator_embeddings = LangchainEmbeddingsWrapper(OpenAIEmbeddings())"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Setup Persona and transforms\n", "you may automatically create personas using this [notebook](./_persona_generator.md). For the sake of simplicity, I am using a pre-defined person, two basic tranforms and simple specic query distribution."]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [], "source": ["from ragas.testset.persona import Persona\n", "\n", "personas = [\n", "    <PERSON>a(\n", "        name=\"curious student\",\n", "        role_description=\"A student who is curious about the world and wants to learn more about different cultures and languages\",\n", "    ),\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ragas.testset.transforms.extractors.llm_based import NERExtractor\n", "from ragas.testset.transforms.splitters import HeadlineSplitter\n", "\n", "transforms = [HeadlineSplitter(), NERExtractor()]"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Intialize test generator"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["from ragas.testset import TestsetGenerator\n", "\n", "generator = TestsetGenerator(\n", "    llm=generator_llm, embedding_model=generator_embeddings, persona_list=personas\n", ")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Load and Adapt Queries\n", "\n", "Here we load the required query types and adapt them to the target language. "]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["from ragas.testset.synthesizers.single_hop.specific import (\n", "    SingleHopSpecificQuerySynthesizer,\n", ")\n", "\n", "distribution = [\n", "    (SingleHopSpecificQuerySynthesizer(llm=generator_llm), 1.0),\n", "]\n", "\n", "for query, _ in distribution:\n", "    prompts = await query.adapt_prompts(\"spanish\", llm=generator_llm)\n", "    query.set_prompts(**prompts)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Generate"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Applying HeadlineSplitter:   0%|          | 0/6 [00:00<?, ?it/s]unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "unable to apply transformation: 'headlines' property not found in this node\n", "Generating Scenarios: 100%|██████████| 1/1 [00:07<00:00,  7.75s/it] \n", "Generating Samples: 100%|██████████| 5/5 [00:03<00:00,  1.65it/s]\n"]}], "source": ["dataset = generator.generate_with_langchain_docs(\n", "    docs[:],\n", "    testset_size=5,\n", "    transforms=transforms,\n", "    query_distribution=distribution,\n", ")"]}, {"cell_type": "code", "execution_count": 22, "metadata": {}, "outputs": [], "source": ["eval_dataset = dataset.to_evaluation_dataset()"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Query: Quelles sont les caractéristiques du Bronx en tant que borough de New York?\n", "Reference: Le Bronx est l'un des cinq arrondissements de New York, qui est la plus grande ville des États-Unis. Bien que le contexte ne fournisse pas de détails spécifiques sur le Bronx, il mentionne que New York est une ville cosmopolite avec de nombreux quartiers ethniques, ce qui pourrait inclure des caractéristiques culturelles variées présentes dans le Bronx.\n"]}], "source": ["print(\"Query:\", eval_dataset[0].user_input)\n", "print(\"Reference:\", eval_dataset[0].reference)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["That's it. You can customize the test generation process as per your requirements."]}, {"cell_type": "markdown", "metadata": {}, "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 2}