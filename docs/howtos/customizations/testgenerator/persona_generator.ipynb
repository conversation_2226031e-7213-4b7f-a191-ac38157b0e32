{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["## Person<PERSON>'s in Testset Generation\n", "\n", "You can add different persona's to the testset generation process by defining the [Persona][ragas.testset.persona.Persona] class with the name and role description of the different persona's that might be relevant to your use case and you want to generate testset for.\n", "\n", "For example, for the [gitlab handbook](https://about.gitlab.com/handbook/) we might want to generate testset for different persona's like a new joinee, a manager, a senior manager, etc. And hence we will define them as follows:\n", "\n", "1. New Joinee: Don't know much about the company and is looking for information on how to get started.\n", "2. Manager: Wants to know about the different teams and how they collaborate with each other.\n", "3. Senior Manager: Wants to know about the company vision and how it is executed.\n", "\n", "Which we can define as follows:"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Persona(name='New Joinee', role_description=\"Don't know much about the company and is looking for information on how to get started.\"),\n", " Persona(name='Manager', role_description='Wants to know about the different teams and how they collaborate with each other.'),\n", " Persona(name='Senior Manager', role_description='Wants to know about the company vision and how it is executed.')]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["from ragas.testset.persona import Persona\n", "\n", "persona_new_joinee = <PERSON>a(\n", "    name=\"New Joinee\",\n", "    role_description=\"Don't know much about the company and is looking for information on how to get started.\",\n", ")\n", "persona_manager = <PERSON><PERSON>(\n", "    name=\"Manager\",\n", "    role_description=\"Wants to know about the different teams and how they collaborate with each other.\",\n", ")\n", "persona_senior_manager = <PERSON><PERSON>(\n", "    name=\"Senior Manager\",\n", "    role_description=\"Wants to know about the company vision and how it is executed.\",\n", ")\n", "\n", "personas = [persona_new_joinee, persona_manager, persona_senior_manager]\n", "personas"]}, {"cell_type": "markdown", "metadata": {}, "source": ["And then you can use these persona's in the testset generation process by passing them to the [TestsetGenerator][ragas.testset.generator.TestsetGenerator] class."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ragas.testset import TestsetGenerator\n", "from ragas.testset.graph import KnowledgeGraph\n", "from ragas.llms import llm_factory\n", "\n", "# Load the knowledge graph\n", "kg = KnowledgeGraph.load(\"../../../../experiments/gitlab_kg.json\")\n", "# Initialize the Generator LLM\n", "llm = llm_factory(\"gpt-4o-mini\")\n", "\n", "# Initialize the Testset Generator\n", "testset_generator = TestsetGenerator(knowledge_graph=kg, persona_list=personas, llm=llm)\n", "# Generate the Testset\n", "testset = testset_generator.generate(testset_size=10)\n", "testset"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>user_input</th>\n", "      <th>reference_contexts</th>\n", "      <th>reference</th>\n", "      <th>synthesizer_name</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>What the Director do in GitLab and how they wo...</td>\n", "      <td>[09db4f3e-1c10-4863-9024-f869af48d3e0\\n\\ntitle...</td>\n", "      <td>The Director at GitLab, such as the Director o...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Wht is the rol of the VP in GitLab?</td>\n", "      <td>[56c84f1b-3558-4c80-b8a9-348e69a4801b\\n\\nJob F...</td>\n", "      <td>The VP, or Vice President, at GitLab is respon...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>What Git<PERSON>ab do for career progression?</td>\n", "      <td>[ead619a5-930f-4e2b-b797-41927a04d2e3\\n\\nGoals...</td>\n", "      <td>The Job frameworks at GitLab help team members...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>Wht is the S-grop and how do they work with ot...</td>\n", "      <td>[42babb12-b033-493f-b684-914e2b1b1d0f\\n\\nPeopl...</td>\n", "      <td>Members of the S-group are expected to demonst...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>How does Google execute its company vision?</td>\n", "      <td>[c3ed463d-1cdc-4ba4-a6ca-2c4ab12da883\\n\\nof mo...</td>\n", "      <td>To effectively execute the company vision, man...</td>\n", "      <td>single_hop_specifc_query_synthesizer</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                          user_input  ...                      synthesizer_name\n", "0  What the Director do in GitLab and how they wo...  ...  single_hop_specifc_query_synthesizer\n", "1                Wht is the rol of the VP in GitLab?  ...  single_hop_specifc_query_synthesizer\n", "2             What GitLab do for career progression?  ...  single_hop_specifc_query_synthesizer\n", "3  Wht is the S-grop and how do they work with ot...  ...  single_hop_specifc_query_synthesizer\n", "4        How does Google execute its company vision?  ...  single_hop_specifc_query_synthesizer\n", "\n", "[5 rows x 4 columns]"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["testset.to_pandas().head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Automatic Persona Generation\n", "\n", "If you want to automatically generate persona's from a knowledge graph, you can use the [generate_personas_from_kg][ragas.testset.persona.generate_personas_from_kg] function.\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ragas.testset.persona import generate_personas_from_kg\n", "from ragas.testset.graph import KnowledgeGraph\n", "from ragas.llms import llm_factory\n", "\n", "kg = KnowledgeGraph.load(\"../../../../experiments/gitlab_kg.json\")\n", "llm = llm_factory(\"gpt-4o-mini\")\n", "\n", "personas = generate_personas_from_kg(kg=kg, llm=llm, num_personas=5)"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/plain": ["[Persona(name='Organizational Development Manager', role_description='Responsible for implementing job frameworks and career development strategies to enhance employee growth and clarify roles within the company.'),\n", " Persona(name='DevSecOps Product Manager', role_description='Responsible for overseeing the development and strategy of DevSecOps solutions, ensuring alignment with company goals and user needs.'),\n", " Persona(name='Product Pricing Analyst', role_description='Responsible for developing and analyzing pricing strategies that align with customer needs and market demands.'),\n", " Persona(name='Site Reliability Engineer', role_description='Responsible for maintaining service reliability and performance, focusing on implementing rate limits to prevent outages and enhance system stability.'),\n", " Persona(name='Security Operations Engineer', role_description=\"Works on enhancing security logging processes and ensuring compliance within GitLab's infrastructure.\")]"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["personas"]}], "metadata": {"kernelspec": {"display_name": "ragas", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 2}