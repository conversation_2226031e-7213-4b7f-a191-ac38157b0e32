{"cells": [{"cell_type": "markdown", "id": "51c3407b-6041-4217-9ef9-a0e619a51603", "metadata": {}, "source": ["# Create custom single-hop queries from your documents"]}, {"cell_type": "markdown", "id": "5fc18fe5", "metadata": {}, "source": ["### Load sample documents\n", "I am using documents from [gitlab handbook](https://huggingface.co/datasets/explodinggradients/Sample_Docs_Markdown). You can download it by running the below command."]}, {"cell_type": "code", "execution_count": 2, "id": "5e3647cd-f754-4f05-a5ea-488b6a6affaf", "metadata": {}, "outputs": [], "source": ["from langchain_community.document_loaders import DirectoryLoader\n", "\n", "\n", "path = \"Sample_Docs_Markdown/\"\n", "loader = DirectoryLoader(path, glob=\"**/*.md\")\n", "docs = loader.load()"]}, {"cell_type": "markdown", "id": "ba780919", "metadata": {}, "source": ["### Create KG\n", "\n", "Create a base knowledge graph with the documents"]}, {"cell_type": "code", "execution_count": 3, "id": "9034eaf0-e6d8-41d1-943b-594331972f69", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/opt/homebrew/Caskroom/miniforge/base/envs/ragas/lib/python3.9/site-packages/tqdm/auto.py:21: TqdmWarning: IProgress not found. Please update jupyter and ipywidgets. See https://ipywidgets.readthedocs.io/en/stable/user_install.html\n", "  from .autonotebook import tqdm as notebook_tqdm\n"]}], "source": ["from ragas.testset.graph import KnowledgeGraph\n", "from ragas.testset.graph import Node, NodeType\n", "\n", "\n", "kg = KnowledgeGraph()\n", "for doc in docs:\n", "    kg.nodes.append(\n", "        Node(\n", "            type=NodeType.DOCUMENT,\n", "            properties={\n", "                \"page_content\": doc.page_content,\n", "                \"document_metadata\": doc.metadata,\n", "            },\n", "        )\n", "    )"]}, {"cell_type": "markdown", "id": "575e5725", "metadata": {}, "source": ["### Set up the LLM and Embedding Model\n", "You may use any of [your choice](/docs/howtos/customizations/customize_models.md), here I am using models from open-ai."]}, {"cell_type": "code", "execution_count": 4, "id": "52f6d1ae-c9ed-4d82-99d7-d130a36e41e8", "metadata": {}, "outputs": [], "source": ["from ragas.llms.base import llm_factory\n", "from ragas.embeddings.base import embedding_factory\n", "\n", "llm = llm_factory()\n", "embedding = embedding_factory()"]}, {"cell_type": "markdown", "id": "af7f9eaa", "metadata": {}, "source": ["### Setup the transforms\n", "\n", "\n", "Here we are using 2 extractors and 2 relationship builders.\n", "- Headline extrator: Extracts headlines from the documents\n", "- Keyphrase extractor: Extracts keyphrases from the documents\n", "- Headline splitter: Splits the document into nodes based on headlines\n"]}, {"cell_type": "code", "execution_count": 5, "id": "1308cf70-486c-4fc3-be9a-2401e9455312", "metadata": {}, "outputs": [], "source": ["from ragas.testset.transforms import apply_transforms\n", "from ragas.testset.transforms import (\n", "    HeadlinesExtractor,\n", "    Headline<PERSON>p<PERSON>ter,\n", "    KeyphrasesExtractor,\n", ")\n", "\n", "\n", "headline_extractor = HeadlinesExtractor(llm=llm)\n", "headline_splitter = HeadlineSplitter(min_tokens=300, max_tokens=1000)\n", "keyphrase_extractor = KeyphrasesExtractor(\n", "    llm=llm, property_name=\"keyphrases\", max_num=10\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "7eb5f52e-4f9f-4333-bc71-ec795bf5dfff", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Applying KeyphrasesExtractor:   6%| | 2/36 [00:01<00:20,  1Property 'keyphrases' already exists in node '514fdc'. Skipping!\n", "Applying KeyphrasesExtractor:  11%| | 4/36 [00:01<00:10,  2Property 'keyphrases' already exists in node '84a0f6'. Skipping!\n", "Applying KeyphrasesExtractor:  64%|▋| 23/36 [00:03<00:01,  Property 'keyphrases' already exists in node '93f19d'. Skipping!\n", "Applying KeyphrasesExtractor:  72%|▋| 26/36 [00:04<00:00, 1Property 'keyphrases' already exists in node 'a126bf'. Skipping!\n", "Applying KeyphrasesExtractor:  81%|▊| 29/36 [00:04<00:00,  Property 'keyphrases' already exists in node 'c230df'. Skipping!\n", "Applying KeyphrasesExtractor:  89%|▉| 32/36 [00:04<00:00, 1Property 'keyphrases' already exists in node '4f2765'. Skipping!\n", "Property 'keyphrases' already exists in node '4a4777'. Skipping!\n", "                                                           \r"]}], "source": ["transforms = [\n", "    headline_extractor,\n", "    headline_splitter,\n", "    keyphrase_extractor,\n", "]\n", "\n", "apply_transforms(kg, transforms=transforms)"]}, {"cell_type": "markdown", "id": "40503f3c", "metadata": {}, "source": ["### Configure personas\n", "\n", "You can also do this automatically by using the [automatic persona generator](/docs/howtos/customizations/testgenerator/_persona_generator.md)"]}, {"cell_type": "code", "execution_count": 7, "id": "213d93e7-1233-4df7-8022-4827b683f0b3", "metadata": {}, "outputs": [], "source": ["from ragas.testset.persona import Persona\n", "\n", "person1 = Persona(\n", "    name=\"gitlab employee\",\n", "    role_description=\"A junior gitlab employee curious on workings on gitlab\",\n", ")\n", "persona2 = Persona(\n", "    name=\"Hiring manager at gitlab\",\n", "    role_description=\"A hiring manager at gitlab trying to underestand hiring policies in gitlab\",\n", ")\n", "persona_list = [person1, persona2]"]}, {"cell_type": "markdown", "id": "d5088c18-a8eb-4180-b066-46a8a795553b", "metadata": {}, "source": ["## "]}, {"cell_type": "markdown", "id": "e3c756d2-1131-4fde-b3a7-b81589d15929", "metadata": {}, "source": ["## SingleHop Query\n", "\n", "Inherit from `SingleHopQuerySynthesizer` and modify the function that generates scenarios for query creation. \n", "\n", "**Steps**:\n", "- find qualified set of nodes for the query creation. Here I am selecting all nodes with keyphrases extracted.\n", "- For each qualified set\n", "    - Match the keyphrase with one or more persona. \n", "    - Create all possible combinations of (Node, Persona, Query Style, Query Length)\n", "    - Samples the required number of queries from the combinations"]}, {"cell_type": "code", "execution_count": 15, "id": "c0a7128c-3840-434d-a1df-9e0835c2eb9b", "metadata": {}, "outputs": [], "source": ["from ragas.testset.synthesizers.single_hop import (\n", "    SingleHopQuerySynthesizer,\n", ")\n", "from dataclasses import dataclass\n", "from ragas.testset.synthesizers.prompts import (\n", "    ThemesPersonasInput,\n", "    ThemesPersonasMatchingPrompt,\n", ")\n", "\n", "\n", "@dataclass\n", "class MySingleHopScenario(SingleHopQuerySynthesizer):\n", "\n", "    theme_persona_matching_prompt = ThemesPersonasMatchingPrompt()\n", "\n", "    async def _generate_scenarios(self, n, knowledge_graph, persona_list, callbacks):\n", "\n", "        property_name = \"keyphrases\"\n", "        nodes = []\n", "        for node in knowledge_graph.nodes:\n", "            if node.type.name == \"CHUNK\" and node.get_property(property_name):\n", "                nodes.append(node)\n", "\n", "        number_of_samples_per_node = max(1, n // len(nodes))\n", "\n", "        scenarios = []\n", "        for node in nodes:\n", "            if len(scenarios) >= n:\n", "                break\n", "            themes = node.properties.get(property_name, [\"\"])\n", "            prompt_input = ThemesPersonasInput(themes=themes, personas=persona_list)\n", "            persona_concepts = await self.theme_persona_matching_prompt.generate(\n", "                data=prompt_input, llm=self.llm, callbacks=callbacks\n", "            )\n", "            base_scenarios = self.prepare_combinations(\n", "                node,\n", "                themes,\n", "                personas=persona_list,\n", "                persona_concepts=persona_concepts.mapping,\n", "            )\n", "            scenarios.extend(\n", "                self.sample_combinations(base_scenarios, number_of_samples_per_node)\n", "            )\n", "\n", "        return scenarios"]}, {"cell_type": "code", "execution_count": 16, "id": "6613ade2-b2bb-466a-800a-9ab8cad61661", "metadata": {}, "outputs": [], "source": ["query = MySingleHopScenario(llm=llm)"]}, {"cell_type": "code", "execution_count": 17, "id": "ca6f997f-355b-423f-8559-d20acfd11a53", "metadata": {}, "outputs": [], "source": ["scenarios = await query.generate_scenarios(\n", "    n=5, knowledge_graph=kg, persona_list=persona_list\n", ")"]}, {"cell_type": "code", "execution_count": 19, "id": "6622721d-74e1-4922-b68d-ce4c29a00c02", "metadata": {}, "outputs": [{"data": {"text/plain": ["SingleHopScenario(\n", "nodes=1\n", "term=what is an ally\n", "persona=name='Hiring manager at gitlab' role_description='A hiring manager at gitlab trying to underestand hiring policies in gitlab'\n", "style=Web search like queries\n", "length=long)"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["scenarios[0]"]}, {"cell_type": "code", "execution_count": null, "id": "ff32bf81", "metadata": {}, "outputs": [], "source": ["result = await query.generate_sample(scenario=scenarios[-1])"]}, {"cell_type": "markdown", "id": "bc5c0fb1", "metadata": {}, "source": ["### Modify prompt to customize the query style\n", "Here I am replacing the default prompt with an instruction to generate only Yes/No questions. This is an optional step. "]}, {"cell_type": "code", "execution_count": 23, "id": "6c5d43df-43ad-4ef4-9c52-37a943198400", "metadata": {}, "outputs": [], "source": ["instruction = \"\"\"Generate a Yes/No query and answer based on the specified conditions (persona, term, style, length) \n", "and the provided context. Ensure the answer is entirely faithful to the context, using only the information \n", "directly from the provided context.\n", "\n", "### Instructions:\n", "1. **Generate a Yes/No Query**: Based on the context, persona, term, style, and length, create a question \n", "that aligns with the persona's perspective, incorporates the term, and can be answered with 'Yes' or 'No'.\n", "2. **Generate an Answer**: Using only the content from the provided context, provide a 'Yes' or 'No' answer \n", "to the query. Do not add any information not included in or inferable from the context.\"\"\""]}, {"cell_type": "code", "execution_count": 25, "id": "4d20f2e7-7870-4dfe-acf1-05feb84adfe7", "metadata": {}, "outputs": [], "source": ["prompt = query.get_prompts()[\"generate_query_reference_prompt\"]\n", "prompt.instruction = instruction\n", "query.set_prompts(**{\"generate_query_reference_prompt\": prompt})"]}, {"cell_type": "code", "execution_count": 26, "id": "855770c7-577b-41df-98c2-d366dd927008", "metadata": {}, "outputs": [], "source": ["result = await query.generate_sample(scenario=scenarios[-1])"]}, {"cell_type": "code", "execution_count": 27, "id": "40254484-4e1d-450e-8d8b-3b9a20a00467", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Does the Diversity, Inclusion & Belonging (DIB) Team at GitLab have a structured approach to encourage collaborations among team members through various communication methods?'"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["result.user_input"]}, {"cell_type": "code", "execution_count": 28, "id": "916c1c5b-c92b-40cc-a1e8-d608e7c080f7", "metadata": {}, "outputs": [{"data": {"text/plain": ["'Yes'"]}, "execution_count": 28, "metadata": {}, "output_type": "execute_result"}], "source": ["result.reference"]}, {"cell_type": "code", "execution_count": null, "id": "4d5fc423-e9e5-4493-b109-d3f5baac7eca", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "ragas", "language": "python", "name": "ragas"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.20"}}, "nbformat": 4, "nbformat_minor": 5}