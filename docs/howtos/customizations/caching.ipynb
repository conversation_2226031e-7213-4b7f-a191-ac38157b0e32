{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Caching in Ragas\n", "\n", "You can use caching to speed up your evaluations and testset generation by avoiding redundant computations. We use Exact Match Caching to cache the responses from the LLM and Embedding models.\n", "\n", "You can use the [DiskCacheBackend][ragas.cache.DiskCacheBackend] which uses a local disk cache to store the cached responses. You can also implement your own custom cacher by implementing the [CacheInterface][ragas.cache.CacheInterface].\n", "\n", "\n", "## Using <PERSON><PERSON><PERSON><PERSON>\n", "\n", "Let's see how you can use the [DiskCacheBackend][ragas.cache.DiskCacheBackend]  LLM and Embedding models.\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"text/plain": ["DiskCacheBackend(cache_dir=.cache)"]}, "execution_count": 1, "metadata": {}, "output_type": "execute_result"}], "source": ["from ragas.cache import DiskCacheBackend\n", "\n", "cacher = DiskCacheBackend()\n", "\n", "# check if the cache is empty and clear it\n", "print(len(cacher.cache))\n", "cacher.cache.clear()\n", "print(len(cacher.cache))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Create an LLM and Embedding model with the cacher, here I'm using the `ChatOpenAI` from [langchain-openai](https://github.com/langchain-ai/langchain-openai) as an example.\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["from langchain_openai import ChatOpenAI\n", "from ragas.llms import LangchainLLMWrapper\n", "\n", "cached_llm = LangchainLLMWrapper(ChatOpenAI(model=\"gpt-4o\"), cache=cacher)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# if you want to see the cache in action, set the logging level to debug\n", "import logging\n", "from ragas.utils import set_logging_level\n", "\n", "set_logging_level(\"ragas.cache\", logging.DEBUG)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Now let's run a simple evaluation."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from ragas import evaluate\n", "from ragas import EvaluationDataset\n", "\n", "from ragas.metrics import FactualCorrectness, AspectCritic\n", "from datasets import load_dataset\n", "\n", "# Define Answer Correctness with AspectCritic\n", "answer_correctness = AspectCritic(\n", "    name=\"answer_correctness\",\n", "    definition=\"Is the answer correct? Does it match the reference answer?\",\n", "    llm=cached_llm,\n", ")\n", "\n", "metrics = [answer_correctness, FactualCorrectness(llm=cached_llm)]\n", "\n", "# load the dataset\n", "dataset = load_dataset(\n", "    \"explodinggradients/amnesty_qa\", \"english_v3\", trust_remote_code=True\n", ")\n", "eval_dataset = EvaluationDataset.from_hf_dataset(dataset[\"eval\"])\n", "\n", "# evaluate the dataset\n", "results = evaluate(\n", "    dataset=eval_dataset,\n", "    metrics=metrics,\n", ")\n", "\n", "results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["This took almost 2mins to run in our local machine. Now let's run it again to see the cache in action."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["results = evaluate(\n", "    dataset=eval_dataset,\n", "    metrics=metrics,\n", ")\n", "\n", "results"]}, {"cell_type": "markdown", "metadata": {}, "source": ["Runs almost instantaneously.\n", "\n", "You can also use this with testset generation also by replacing the `generator_llm` with a cached version of it. Refer to the [testset generation](../../getstarted/rag_testset_generation.md) section for more details."]}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.15"}}, "nbformat": 4, "nbformat_minor": 2}