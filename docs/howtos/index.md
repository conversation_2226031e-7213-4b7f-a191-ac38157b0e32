# 🛠️ How-to Guides

Each guide in this section provides a focused solution to real-world problems that you, as an experienced user, may encounter while using Ragas. These guides are designed to be concise and direct, offering quick solutions to your problems. We assume you have a foundational understanding and are comfortable with Ragas concepts. If not, feel free to explore the [Get Started](../getstarted/index.md) section first.

<div class="grid cards" markdown>

-   :material-tune:{ .lg .middle } [__Customization__](customizations/index.md)

    ---

    How to customize various aspects of Ragas to suit your needs.
    
    Customize features such as [Metrics](customizations/index.md#metrics) and [Testset Generation](customizations/index.md#testset-generation).

-   :material-cube-outline:{ .lg .middle }  [__Applications__](applications/index.md)

    ---

    How to use Ragas for various applications and use cases.

    Includes applications such as [RAG evaluation](applications/index.md).

-   :material-link-variant:{ .lg .middle } [__Integrations__](integrations/index.md)

    ---

    How to integrate Ragas with other frameworks and observability tools.

    Use Ragas with frameworks like [Langchain](integrations/langchain.md), [LlamaIndex](integrations/_llamaindex.md), and [observability tools](./observability.md).

</div>
