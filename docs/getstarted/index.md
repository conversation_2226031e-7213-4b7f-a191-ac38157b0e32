# 🚀 Get Started

Welcome to the Ragas tutorials! If you're new to Ragas, the Get Started guides will walk you through the fundamentals of working with Ragas. These tutorials assume basic knowledge of Python and building LLM application pipelines. 

Before you proceed further, ensure that you have [Ragas installed](./install.md)!

!!! note
    The tutorials only provide an overview of what you can accomplish with Ragas and the basic skills needed to utilize it effectively. For an in-depth explanation of the core concepts behind Ragas, check out the [Core Concepts](../concepts/index.md) page. You can also explore the [How-to Guides](../howtos/index.md) for specific applications of Ragas.

If you have any questions about Ragas, feel free to join and ask in the `#questions` channel in our Discord community.

Let's get started!

- [Evaluate your first AI app](./evals.md)
- [Run ragas metrics for evaluating RAG](rag_eval.md)
- [Generate test data for evaluating RAG](rag_testset_generation.md)